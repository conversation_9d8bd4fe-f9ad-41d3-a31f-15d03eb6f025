services:
  # Database service
  database:
    image: mysql:8.0
    container_name: fxd-erp-database
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: fxd_erp
      MYSQL_USER: fxd_user
      MY<PERSON>QL_PASSWORD: fxd_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - fxd-erp-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "fxd_user", "-pfxd_password"]
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s

  # Backend service
  backend:
    build:
      context: ./FxDPartnerERPBackend
      dockerfile: Dockerfile
    container_name: fxd-erp-backend
    restart: unless-stopped
    ports:
      - "3001:3001"
    depends_on:
      database:
        condition: service_healthy
    environment:
      - DB_HOST=database
      - DB_PORT=3306
      - DB_USER=fxd_user
      - DB_PASSWORD=fxd_password
      - DB_NAME=fxd_partner_erp_backend_try
      - NODE_ENV=production
    volumes:
      - ./FxDPartnerERPBackend/.env.docker:/app/.env:ro
    networks:
      - fxd-erp-network

  # Frontend service
  frontend:
    build:
      context: ./FxDPartnerERP
      dockerfile: Dockerfile
    container_name: fxd-erp-frontend
    restart: unless-stopped
    ports:
      - "5173:80"
    environment:
      - VITE_API_URL=http://localhost:3000
      - VITE_ORGANIZATION_ID=default-org-id
      - VITE_API_TIMEOUT=10000
      - VITE_APP_NAME=FxD Partner ERP
      - VITE_APP_VERSION=1.0.0
      - VITE_ENABLE_DEBUG=false
      - VITE_ENABLE_ANALYTICS=false
    depends_on:
      - backend
    networks:
      - fxd-erp-network

volumes:
  mysql_data:
    driver: local

networks:
  fxd-erp-network:
    driver: bridge
