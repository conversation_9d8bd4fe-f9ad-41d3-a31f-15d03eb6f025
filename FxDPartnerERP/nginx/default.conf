server {
    listen 80;
    
    # Redirect root to the app base path
    location = / {
        return 301 /FxDPartnerERP/;
    }
    
    # Main application location
    location /FxDPartnerERP/ {
        alias /usr/share/nginx/html/;
        index index.html;
        try_files $uri $uri/ /FxDPartnerERP/index.html;
        
        # Ensure proper MIME types for JavaScript modules
        location ~* \.(js|mjs)$ {
            add_header Content-Type application/javascript;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        location ~* \.(css)$ {
            add_header Content-Type text/css;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Handle static assets directly
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root /usr/share/nginx/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
