/* Custom styles for react-datepicker to improve mobile experience */

/* Make the calendar responsive on mobile */
@media (max-width: 640px) {
  .react-datepicker {
    font-size: 0.9rem;
    width: 100%;
    max-width: 320px;
  }
  
  .react-datepicker__month-container {
    width: 100%;
  }
  
  .react-datepicker__day-name,
  .react-datepicker__day,
  .react-datepicker__time-list-item {
    width: 2.5rem;
    height: 2.5rem;
    line-height: 2.5rem;
    margin: 0.1rem;
  }
  
  .react-datepicker__time-container {
    width: 100px;
  }
  
  .react-datepicker__time-list-item {
    padding: 0.5rem;
  }
}

/* General improvements for all screen sizes */
.react-datepicker-wrapper {
  width: 100%;
}

.react-datepicker__input-container {
  width: 100%;
}

.react-datepicker {
  font-family: inherit;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.react-datepicker__header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 0.375rem 0.375rem 0 0;
  padding-top: 0.5rem;
}

.react-datepicker__current-month {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.5rem;
}

.react-datepicker__day-name {
  color: #6b7280;
  font-weight: 500;
}

.react-datepicker__day {
  color: #374151;
  border-radius: 0.375rem;
  transition: all 0.2s;
}

.react-datepicker__day:hover {
  background-color: #f3f4f6;
}

.react-datepicker__day--selected {
  background-color: #16a34a !important;
  color: white !important;
  font-weight: 500;
}

.react-datepicker__day--selected:hover {
  background-color: #15803d !important;
}

.react-datepicker__day--keyboard-selected {
  background-color: #dcfce7;
  color: #16a34a;
}

.react-datepicker__day--today {
  font-weight: 600;
  color: #16a34a;
}

.react-datepicker__day--disabled {
  color: #d1d5db;
  cursor: not-allowed;
}

.react-datepicker__day--disabled:hover {
  background-color: transparent;
}

.react-datepicker__time-container {
  border-left: 1px solid #e5e7eb;
}

.react-datepicker__time-list-item {
  transition: all 0.2s;
}

.react-datepicker__time-list-item:hover {
  background-color: #f3f4f6;
}

.react-datepicker__time-list-item--selected {
  background-color: #16a34a !important;
  color: white !important;
  font-weight: 500;
}

.react-datepicker__time-list-item--selected:hover {
  background-color: #15803d !important;
}

.react-datepicker__navigation {
  top: 0.75rem;
}

.react-datepicker__navigation-icon::before {
  border-color: #6b7280;
}

.react-datepicker__navigation:hover .react-datepicker__navigation-icon::before {
  border-color: #374151;
}

/* Improve touch targets for mobile */
@media (pointer: coarse) {
  .react-datepicker__day,
  .react-datepicker__time-list-item {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* Portal positioning for mobile */
.react-datepicker-popper {
  z-index: 50;
}

@media (max-width: 640px) {
  .react-datepicker-popper[data-placement^="bottom"] {
    padding-top: 0;
  }
  
  .react-datepicker-popper[data-placement^="top"] {
    padding-bottom: 0;
  }
}
