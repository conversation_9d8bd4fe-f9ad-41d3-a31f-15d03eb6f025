import React, { useState, useEffect, useRef } from 'react';
import { Search, Plus, ChevronDown, Check } from 'lucide-react';
import { productService } from '../../services/api/productService';
import { toast } from 'react-hot-toast';

interface Product {
  id: string;
  name: string;
  category: string;
  description?: string;
  skus?: SKU[];
}

interface SKU {
  id: string;
  code: string;
  unit_type: 'box' | 'loose';
  unit_weight?: number;
  status: string;
}

interface ProductSelectorProps {
  value?: Product | null;
  onChange: (product: Product | null) => void;
  onCreateNew: (productName?: string) => void;
  placeholder?: string;
  disabled?: boolean;
  showCreateOption?: boolean;
}

const ProductSelector: React.FC<ProductSelectorProps> = ({
  value,
  onChange,
  onCreateNew,
  placeholder = "Search for a product...",
  disabled = false,
  showCreateOption = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [recentProducts, setRecentProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [showRecent, setShowRecent] = useState(true);
  
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // Load recent products on mount
  useEffect(() => {
    loadRecentProducts();
  }, []);

  // Handle search with debouncing
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (searchQuery.trim()) {
      searchTimeoutRef.current = setTimeout(() => {
        searchProducts(searchQuery);
      }, 300);
    } else {
      setProducts([]);
      setShowRecent(true);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadRecentProducts = async () => {
    try {
      const data = await productService.getRecent();
      setRecentProducts(data || []);
    } catch (error) {
      console.error('Error loading recent products:', error);
    }
  };

  const searchProducts = async (query: string) => {
    if (!query.trim()) return;
    
    setLoading(true);
    try {
      const data = await productService.searchWithSKUs(query);
      setProducts(data || []);
      setShowRecent(false);
    } catch (error) {
      console.error('Error searching products:', error);
      toast.error('Failed to search products');
    } finally {
      setLoading(false);
    }
  };

  const handleInputFocus = () => {
    setIsOpen(true);
    if (!searchQuery && recentProducts.length > 0) {
      setShowRecent(true);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    setIsOpen(true);
    // Clear the selected value if user is typing
    if (value && query !== value.name) {
      onChange(null);
    }
  };

  const handleProductSelect = (product: Product) => {
    onChange(product);
    setSearchQuery(product.name);
    setIsOpen(false);
  };

  const handleCreateNew = () => {
    onCreateNew(searchQuery);
    setIsOpen(false);
  };

  const handleClear = () => {
    onChange(null);
    setSearchQuery('');
    setProducts([]);
    setShowRecent(true);
    inputRef.current?.focus();
  };

  const displayProducts = showRecent ? recentProducts : products;

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={value ? value.name : searchQuery}
          onChange={handleInputChange}
          onFocus={handleInputFocus}
          placeholder={placeholder}
          disabled={disabled}
          className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
        />
        
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        
        {value ? (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        ) : (
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        )}
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
          {loading && (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              Searching...
            </div>
          )}

          {!loading && showRecent && recentProducts.length > 0 && (
            <>
              <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b">
                Recent Products
              </div>
              {recentProducts.map((product) => (
                <button
                  key={product.id}
                  type="button"
                  onClick={() => handleProductSelect(product)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">{product.name}</div>
                      <div className="text-sm text-gray-500">{product.category}</div>
                      {product.skus && product.skus.length > 0 && (
                        <div className="text-xs text-gray-400 mt-1">
                          {product.skus.length} SKU{product.skus.length !== 1 ? 's' : ''} available
                        </div>
                      )}
                    </div>
                    {value?.id === product.id && (
                      <Check className="h-4 w-4 text-green-600" />
                    )}
                  </div>
                </button>
              ))}
            </>
          )}

          {!loading && !showRecent && products.length > 0 && (
            <>
              <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b">
                Search Results
              </div>
              {products.map((product) => (
                <button
                  key={product.id}
                  type="button"
                  onClick={() => handleProductSelect(product)}
                  className="w-full px-4 py-3 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">{product.name}</div>
                      <div className="text-sm text-gray-500">{product.category}</div>
                      {product.skus && product.skus.length > 0 && (
                        <div className="text-xs text-gray-400 mt-1">
                          {product.skus.length} SKU{product.skus.length !== 1 ? 's' : ''} available
                        </div>
                      )}
                    </div>
                    {value?.id === product.id && (
                      <Check className="h-4 w-4 text-green-600" />
                    )}
                  </div>
                </button>
              ))}
            </>
          )}

          {!loading && displayProducts.length === 0 && searchQuery && (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              No products found for "{searchQuery}"
            </div>
          )}

          {!loading && displayProducts.length === 0 && !searchQuery && (
            <div className="px-4 py-3 text-sm text-gray-500 text-center">
              No recent products found
            </div>
          )}

          {showCreateOption && (
            <button
              type="button"
              onClick={handleCreateNew}
              className="w-full px-4 py-3 text-left hover:bg-green-50 focus:bg-green-50 focus:outline-none border-t border-gray-200 text-green-600 font-medium"
            >
              <div className="flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Create New Product
                {searchQuery && ` "${searchQuery}"`}
              </div>
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ProductSelector;
