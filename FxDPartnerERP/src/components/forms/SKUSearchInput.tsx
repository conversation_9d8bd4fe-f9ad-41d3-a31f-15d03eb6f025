import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Search, ChevronDown, Package, AlertTriangle, Tag } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface InventoryItem {
  id: string;
  product_id: string;
  product_name: string;
  product_category: string;
  sku_id: string;
  sku_code: string;
  unit_type: string;
  available_quantity: number;
  total_weight: number;
}

interface SKUSearchInputProps {
  inventory: InventoryItem[];
  value: string;
  onChange: (inventoryId: string, selectedItem?: InventoryItem) => void;
  placeholder?: string;
  className?: string;
  onAdjustInventory?: (item: InventoryItem) => void;
}

const SKUSearchInput: React.FC<SKUSearchInputProps> = ({
  inventory,
  value,
  onChange,
  placeholder = "Search by SKU code...",
  className = "",
  onAdjustInventory
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [recentSKUs, setRecentSKUs] = useState<InventoryItem[]>([]);
  const [showRecent, setShowRecent] = useState(true);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // Get the currently selected item by unique ID
  const selectedItem = inventory.find(item => item.id === value);

  // Load recent SKUs on mount (last 10 used SKUs based on updated inventory)
  useEffect(() => {
    const recent = inventory
      .sort((a, b) => b.sku_code.localeCompare(a.sku_code))
      .slice(0, 10);
    setRecentSKUs(recent);
  }, [inventory]);

  // Update input value when selection changes
  useEffect(() => {
    if (selectedItem) {
      setInputValue(selectedItem.sku_code);
    } else {
      setInputValue('');
    }
  }, [selectedItem]);

  // Filter items based on input value with SKU-first priority
  const filteredItems = React.useMemo(() => {
    if (!inputValue.trim()) {
      return showRecent ? recentSKUs : [];
    }

    const searchTerms = inputValue.toLowerCase().split(/\s+/);
    
    // Prioritize SKU code matches, then product name matches
    const skuMatches: InventoryItem[] = [];
    const productMatches: InventoryItem[] = [];
    
    inventory.forEach(item => {
      const skuCode = (item.sku_code || '').toLowerCase();
      const productName = (item.product_name || '').toLowerCase();
      const category = (item.product_category || '').toLowerCase();
      
      const skuMatchesAll = searchTerms.every(term => skuCode.includes(term));
      const productMatchesAll = searchTerms.every(term => 
        productName.includes(term) || category.includes(term)
      );
      
      if (skuMatchesAll) {
        skuMatches.push(item);
      } else if (productMatchesAll) {
        productMatches.push(item);
      }
    });

    // Combine with SKU matches first, then product matches
    const combined = [...skuMatches, ...productMatches].slice(0, 50);
    
    // Debug logging for SKU searches
    if (inputValue.toLowerCase().includes('premium')) {
      console.log('🔍 SKUSearchInput - Filtering for Premium SKUs:', {
        searchInput: inputValue,
        totalInventory: inventory.length,
        skuMatches: skuMatches.length,
        productMatches: productMatches.length,
        totalResults: combined.length,
        premiumItems: combined.filter(item => item.sku_code === 'Premium').map(item => ({
          product_name: item.product_name,
          sku_code: item.sku_code,
          sku_id: item.sku_id,
          displayText: `${item.sku_code} - ${item.product_name}`
        }))
      });
    }

    setShowRecent(false);
    return combined;
  }, [inputValue, inventory, recentSKUs, showRecent]);

  // Handle input changes with debouncing
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setIsOpen(true);
    setSelectedIndex(-1);

    // Clear timeout if exists
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Clear selection if user is typing something different from the selected item
    if (selectedItem) {
      const expectedValue = selectedItem.sku_code;
      
      if ((newValue !== expectedValue && newValue !== expectedValue.substring(0, newValue.length)) || newValue === '') {
        console.log('Clearing SKU selection due to input change:', {
          newValue,
          expectedValue,
          selectedItem: selectedItem.sku_id
        });
        onChange('', undefined);
      }
    }

    // Set debounced search
    if (newValue.trim()) {
      searchTimeoutRef.current = setTimeout(() => {
        setShowRecent(false);
      }, 300);
    } else {
      setShowRecent(true);
    }
  };

  // Handle item selection
  const handleSelectItem = (item: InventoryItem) => {
    // Enhanced validation that the item exists in current inventory
    const itemExists = inventory.find(inv =>
      inv.sku_id === item.sku_id &&
      inv.product_id === item.product_id
    );

    if (!itemExists) {
      console.error('Selected SKU not found in inventory:', item);
      toast.error('Selected SKU not found in current inventory. Please refresh and try again.');
      return;
    }

    // Additional validation for required fields
    if (!item.sku_id || !item.product_id || !item.product_name || !item.sku_code) {
      console.error('Selected SKU has missing required fields:', item);
      toast.error('Selected SKU has incomplete data. Please contact support.');
      return;
    }

    console.log('🎯 SKUSearchInput - Processing selected SKU:', {
      originalItem: {
        sku_id: item.sku_id,
        product_name: item.product_name,
        sku_code: item.sku_code,
        product_id: item.product_id
      }
    });

    // Create a clean copy to ensure no reference issues
    const selectedItemCopy: InventoryItem = {
      id: item.id,
      product_id: item.product_id,
      product_name: item.product_name,
      product_category: item.product_category,
      sku_id: item.sku_id,
      sku_code: item.sku_code,
      unit_type: item.unit_type,
      available_quantity: item.available_quantity,
      total_weight: item.total_weight
    };

    console.log('📤 SKUSearchInput - About to call onChange with:', {
      skuIdParam: item.sku_id,
      selectedItemParam: {
        product_id: selectedItemCopy.product_id,
        product_name: selectedItemCopy.product_name,
        sku_code: selectedItemCopy.sku_code,
        sku_id: selectedItemCopy.sku_id
      },
      expectedResult: `Should show "${selectedItemCopy.sku_code}" for "${selectedItemCopy.product_name}" in table`
    });

    // Pass the unique ID to ensure correct selection
    onChange(item.id, selectedItemCopy);
    setInputValue(item.sku_code);

    setIsOpen(false);
    setSelectedIndex(-1);
    setShowRecent(true);
    inputRef.current?.blur();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown' || e.key === 'Enter') {
        e.preventDefault();
        setIsOpen(true);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < filteredItems.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : 0);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && filteredItems[selectedIndex]) {
          handleSelectItem(filteredItems[selectedIndex]);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  // Handle focus/blur
  const handleFocus = () => {
    setIsOpen(true);
    if (!inputValue && recentSKUs.length > 0) {
      setShowRecent(true);
    }
  };

  const handleBlur = (e: React.FocusEvent) => {
    // Don't close if clicking inside dropdown
    if (dropdownRef.current?.contains(e.relatedTarget as Node)) {
      return;
    }

    // Use timeout to allow click events to process
    setTimeout(() => {
      setIsOpen(false);
      // Restore selected item text if we have a selection and input doesn't match
      if (selectedItem) {
        const expectedValue = selectedItem.sku_code;
        if (inputValue !== expectedValue) {
          console.log('Restoring SKU input value on blur:', {
            currentInput: inputValue,
            expectedValue,
            selectedItem: selectedItem.sku_id
          });
          setInputValue(expectedValue);
        }
      }
    }, 200);
  };

  // Handle adjust inventory click
  const handleAdjustInventory = (e: React.MouseEvent, item: InventoryItem) => {
    e.stopPropagation();
    if (onAdjustInventory) {
      onAdjustInventory(item);
    }
  };

  // Create portal root if it doesn't exist
  useEffect(() => {
    if (!document.getElementById('portal-root')) {
      const portalRoot = document.createElement('div');
      portalRoot.id = 'portal-root';
      document.body.appendChild(portalRoot);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Get portal root
  const portalRoot = document.getElementById('portal-root');

  // Calculate dropdown position
  const dropdownPosition = React.useMemo(() => {
    if (!isOpen || !inputRef.current) return null;

    const rect = inputRef.current.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;
    
    // Show dropdown above if there's more space above and not enough below
    const showAbove = spaceBelow < 200 && spaceAbove > spaceBelow;
    
    return {
      top: showAbove ? rect.top + window.scrollY - 200 : rect.bottom + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width,
      maxHeight: showAbove ? Math.min(spaceAbove - 10, 300) : Math.min(spaceBelow - 10, 300)
    };
  }, [isOpen]);

  // Render dropdown
  const renderDropdown = () => {
    if (!isOpen || !portalRoot || !dropdownPosition) return null;

    const displayItems = filteredItems;

    return createPortal(
      <div
        ref={dropdownRef}
        style={{
          position: 'fixed',
          top: dropdownPosition.top,
          left: dropdownPosition.left,
          width: dropdownPosition.width,
          maxHeight: dropdownPosition.maxHeight,
          zIndex: 9999
        }}
        className="bg-white border border-gray-300 rounded-md shadow-lg overflow-auto"
      >
        {displayItems.length > 0 ? (
          <>
            {showRecent && recentSKUs.length > 0 && (
              <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b">
                Recent SKUs
              </div>
            )}
            {!showRecent && inputValue && (
              <div className="px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b">
                Search Results
              </div>
            )}
            <ul className="py-1">
              {displayItems.map((item, index) => (
                <li
                  key={`${item.product_id}_${item.sku_id}_${index}`}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔍 SKU DROPDOWN ITEM CLICKED:', {
                      index,
                      clickedItem: {
                        product_id: item.product_id,
                        product_name: item.product_name,
                        sku_code: item.sku_code,
                        sku_id: item.sku_id
                      },
                      userExpectedSelection: `${item.sku_code} - ${item.product_name}`
                    });
                    handleSelectItem(item);
                  }}
                  className={`px-3 py-2 cursor-pointer transition-colors duration-150 ${
                    index === selectedIndex
                      ? 'bg-green-50 text-green-900'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Tag className="h-4 w-4 text-blue-400 mr-2" />
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {item.sku_code}
                        </div>
                        <div className="text-xs text-gray-500">
                          {item.product_name} • {item.product_category || 'Uncategorized'} • {item.unit_type}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`text-xs ${
                        item.available_quantity < 0 ? 'text-red-600 font-medium' : 'text-gray-500'
                      }`}>
                        {item.available_quantity} {item.unit_type === 'box' ? 'boxes' : 'kg'}
                      </div>
                      {item.available_quantity < 0 && onAdjustInventory && (
                        <button
                          onClick={(e) => handleAdjustInventory(e, item)}
                          className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 transition-colors"
                          title="Adjust Inventory"
                        >
                          <AlertTriangle className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </>
        ) : (
          <div className="px-3 py-2 text-sm text-gray-500 text-center">
            {inputValue.trim() === '' ? 'Start typing to search SKUs...' : 'No SKUs found'}
          </div>
        )}
      </div>,
      portalRoot
    );
  };

  return (
    <div className="relative">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          className={`block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 ${className}`}
          autoComplete="off"
        />
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>
      </div>
      {renderDropdown()}
    </div>
  );
};

export default SKUSearchInput;
