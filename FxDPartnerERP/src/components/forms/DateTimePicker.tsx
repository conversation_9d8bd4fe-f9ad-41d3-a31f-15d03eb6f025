import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Calendar } from 'lucide-react';

interface DateTimePickerProps {
  id?: string;
  name?: string;
  value: string;
  onChange: (value: string) => void;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
}

const DateTimePicker: React.FC<DateTimePickerProps> = ({
  id,
  name,
  value,
  onChange,
  label,
  required = false,
  disabled = false,
  placeholder = 'Select date and time',
  minDate,
  maxDate,
  className = ''
}) => {
  // Convert string value to Date object
  const dateValue = value ? new Date(value) : null;

  // Handle date change and convert back to string
  const handleChange = (date: Date | null) => {
    if (date) {
      // Format as YYYY-MM-DDTHH:mm for datetime-local compatibility
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}T${hours}:${minutes}`;
      onChange(formattedDate);
    } else {
      onChange('');
    }
  };

  return (
    <div className={className}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      )}
      <div className="relative">
        <DatePicker
          id={id}
          name={name}
          selected={dateValue}
          onChange={handleChange}
          showTimeSelect
          timeFormat="HH:mm"
          timeIntervals={15}
          dateFormat="dd/MM/yyyy, h:mm aa"
          placeholderText={placeholder || "Select arrival date and time"}
          disabled={disabled}
          minDate={minDate}
          maxDate={maxDate}
          required={required}
          className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 pr-10 focus:outline-none focus:ring-green-500 focus:border-green-500 text-gray-900 placeholder-gray-500"
          wrapperClassName="w-full"
          popperClassName="react-datepicker-popper"
          showPopperArrow={false}
          autoComplete="off"
        />
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
          <Calendar className="h-5 w-5 text-gray-400" />
        </div>
      </div>
    </div>
  );
};

export default DateTimePicker;
