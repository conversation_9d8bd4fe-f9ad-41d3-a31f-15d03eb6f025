import React, { useState } from 'react';
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  DollarSign,
  ChevronDown,
  RotateCcw
} from 'lucide-react';
import { PaymentFilters as PaymentFiltersType } from '../../types/payment.types';

interface PaymentFiltersProps {
  filters: PaymentFiltersType;
  onUpdateFilter: <K extends keyof PaymentFiltersType>(key: K, value: PaymentFiltersType[K]) => void;
  onUpdateDateRange: (from: string, to: string) => void;
  onUpdateAmountRange: (min: number | null, max: number | null) => void;
  onClearFilters: () => void;
  onSetQuickDateFilter: (period: 'today' | 'week' | 'month' | 'quarter' | 'year') => void;
  activeFiltersCount: number;
  hasActiveFilters: boolean;
  filterOptions: {
    modes: string[];
    partyTypes: string[];
    statuses: string[];
  };
}

const PaymentFilters: React.FC<PaymentFiltersProps> = ({
  filters,
  onUpdateFilter,
  onUpdateDateRange,
  onUpdateAmountRange,
  onClearFilters,
  onSetQuickDateFilter,
  activeFiltersCount,
  hasActiveFilters,
  filterOptions
}) => {
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showAmountRange, setShowAmountRange] = useState(false);

  const formatModeDisplay = (mode: string) => {
    switch (mode) {
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'upi':
        return 'UPI';
      default:
        return mode.charAt(0).toUpperCase() + mode.slice(1);
    }
  };

  const quickDateFilters = [
    { label: 'Today', value: 'today' as const },
    { label: 'This Week', value: 'week' as const },
    { label: 'This Month', value: 'month' as const },
    { label: 'This Quarter', value: 'quarter' as const },
    { label: 'This Year', value: 'year' as const }
  ];

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 space-y-4">
      {/* Search and Basic Filters */}
      <div className="flex flex-col space-y-4 lg:space-y-0 lg:flex-row lg:items-center lg:justify-between lg:gap-4">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
            placeholder="Search payments, parties, references..."
            value={filters.search}
            onChange={(e) => onUpdateFilter('search', e.target.value)}
          />
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          <select
            className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 min-w-0"
            value={filters.type}
            onChange={(e) => onUpdateFilter('type', e.target.value as any)}
          >
            <option value="all">All Types</option>
            <option value="received">Received</option>
            <option value="made">Made</option>
            <option value="expense">Expense</option>
          </select>

          <button
            onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-colors ${
              showAdvancedFilters || hasActiveFilters
                ? 'bg-green-50 border-green-200 text-green-700'
                : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Filter className="h-4 w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Advanced</span>
            {activeFiltersCount > 0 && (
              <span className="ml-1 sm:ml-2 bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full">
                {activeFiltersCount}
              </span>
            )}
            <ChevronDown className={`h-4 w-4 ml-1 transition-transform ${
              showAdvancedFilters ? 'rotate-180' : ''
            }`} />
          </button>

          {hasActiveFilters && (
            <button
              onClick={onClearFilters}
              className="flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg border border-red-200 transition-colors"
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">Clear</span>
            </button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvancedFilters && (
        <div className="border-t border-gray-200 pt-4 space-y-4">
          {/* Date Range Filters */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Date Range</span>
            </div>
            
            {/* Quick Date Filters */}
            <div className="flex flex-wrap gap-2">
              {quickDateFilters.map((filter) => (
                <button
                  key={filter.value}
                  onClick={() => onSetQuickDateFilter(filter.value)}
                  className="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                >
                  {filter.label}
                </button>
              ))}
            </div>

            {/* Custom Date Range */}
            <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
              <input
                type="date"
                className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 flex-1"
                value={filters.dateRange.from}
                onChange={(e) => onUpdateDateRange(e.target.value, filters.dateRange.to)}
                placeholder="From date"
              />
              <span className="text-gray-500 text-sm text-center sm:text-left">to</span>
              <input
                type="date"
                className="border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 flex-1"
                value={filters.dateRange.to}
                onChange={(e) => onUpdateDateRange(filters.dateRange.from, e.target.value)}
                placeholder="To date"
              />
              {(filters.dateRange.from || filters.dateRange.to) && (
                <button
                  onClick={() => onUpdateDateRange('', '')}
                  className="p-1 text-gray-400 hover:text-gray-600 self-center sm:self-auto"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>

          {/* Additional Filters */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Payment Mode */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Payment Mode
              </label>
              <select
                className="w-full border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                value={filters.mode}
                onChange={(e) => onUpdateFilter('mode', e.target.value as any)}
              >
                <option value="all">All Modes</option>
                {filterOptions.modes.map((mode) => (
                  <option key={mode} value={mode}>
                    {formatModeDisplay(mode)}
                  </option>
                ))}
              </select>
            </div>

            {/* Party Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Party Type
              </label>
              <select
                className="w-full border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                value={filters.partyType}
                onChange={(e) => onUpdateFilter('partyType', e.target.value as any)}
              >
                <option value="all">All Types</option>
                <option value="customer">Customer</option>
                <option value="supplier">Supplier</option>
              </select>
            </div>

            {/* Amount Range */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Amount Range
                </label>
                <button
                  onClick={() => setShowAmountRange(!showAmountRange)}
                  className="text-xs text-green-600 hover:text-green-700"
                >
                  {showAmountRange ? 'Hide' : 'Show'}
                </button>
              </div>
              
              {showAmountRange && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <input
                      type="number"
                      className="flex-1 border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      placeholder="Min amount"
                      value={filters.amountRange.min || ''}
                      onChange={(e) => onUpdateAmountRange(
                        e.target.value ? Number(e.target.value) : null,
                        filters.amountRange.max
                      )}
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <input
                      type="number"
                      className="flex-1 border border-gray-300 rounded-lg text-sm py-2 px-3 bg-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      placeholder="Max amount"
                      value={filters.amountRange.max || ''}
                      onChange={(e) => onUpdateAmountRange(
                        filters.amountRange.min,
                        e.target.value ? Number(e.target.value) : null
                      )}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentFilters;
