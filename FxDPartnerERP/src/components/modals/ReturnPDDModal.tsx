import React, { useState, useEffect } from 'react';
import { Package, X, Plus, Minus, Calculator } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { createGRNReturnPDDRequest } from '../../services/api';

interface SalesOrderItem {
  id: string;
  product_name: string;
  sku_code: string;
  quantity: number;
  unit_type: string;
  unit_price: number;
  total_price: number;
}

interface SalesOrderData {
  id: string;
  order_number: string;
  customer: {
    name: string;
  };
  total_amount: number;
  items: SalesOrderItem[];
}

interface ReturnPDDItem {
  sales_order_item_id: string;
  original_quantity: number;
  return_quantity: number;
  pdd_percentage: number;
  pdd_amount: number;
}

interface ReturnPDDModalProps {
  isOpen: boolean;
  onClose: () => void;
  salesOrder: SalesOrderData;
  onSuccess: () => void;
}

const ReturnPDDModal: React.FC<ReturnPDDModalProps> = ({
  isOpen,
  onClose,
  salesOrder,
  onSuccess
}) => {
  const [requestType, setRequestType] = useState<'return' | 'pdd' | 'both'>('both');
  const [items, setItems] = useState<ReturnPDDItem[]>([]);
  const [notes, setNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [globalPDDPercentage, setGlobalPDDPercentage] = useState<number>(0);

  useEffect(() => {
    if (isOpen && salesOrder.items) {
      // Initialize items with default values
      const initialItems = salesOrder.items.map(item => ({
        sales_order_item_id: item.id,
        original_quantity: item.quantity,
        return_quantity: 0,
        pdd_percentage: 0,
        pdd_amount: 0
      }));
      setItems(initialItems);
    }
  }, [isOpen, salesOrder.items]);

  const updateItem = (index: number, field: keyof ReturnPDDItem, value: number) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Auto-calculate PDD amount when percentage changes
    if (field === 'pdd_percentage') {
      const originalItem = salesOrder.items[index];
      const pddAmount = (originalItem.total_price * value) / 100;
      updatedItems[index].pdd_amount = Math.round(pddAmount * 100) / 100;
    }
    
    // Auto-calculate PDD percentage when amount changes
    if (field === 'pdd_amount') {
      const originalItem = salesOrder.items[index];
      const pddPercentage = originalItem.total_price > 0 ? (value / originalItem.total_price) * 100 : 0;
      updatedItems[index].pdd_percentage = Math.round(pddPercentage * 100) / 100;
    }
    
    setItems(updatedItems);
  };

  const getTotalReturnValue = () => {
    return items.reduce((total, item, index) => {
      const originalItem = salesOrder.items[index];
      const returnValue = (originalItem.unit_price * item.return_quantity);
      return total + returnValue;
    }, 0);
  };

  const getTotalPDDAmount = () => {
    return items.reduce((total, item) => total + item.pdd_amount, 0);
  };

  const hasValidItems = () => {
    return items.some(item => 
      (requestType === 'return' || requestType === 'both') && item.return_quantity > 0 ||
      (requestType === 'pdd' || requestType === 'both') && item.pdd_amount > 0
    );
  };

  const handleSubmit = async () => {
    if (!hasValidItems()) {
      toast.error('Please specify return quantities or PDD amounts for at least one item');
      return;
    }

    setIsProcessing(true);
    try {
      const requestData = {
        sales_order_id: salesOrder.id,
        request_type: requestType,
        items: items.filter(item => 
          item.return_quantity > 0 || item.pdd_amount > 0
        ),
        notes: notes.trim() || undefined
      };

      await createGRNReturnPDDRequest(requestData);
      toast.success('Return/PDD request submitted successfully');
      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error creating Return/PDD request:', error);
      toast.error('Failed to submit Return/PDD request');
    } finally {
      setIsProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-orange-100">
                <Package className="h-6 w-6 text-orange-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-medium text-gray-900">
                  Return/PDD Request - {salesOrder.order_number}
                </h3>
                <p className="text-sm text-gray-500">
                  Customer: {salesOrder.customer.name}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Request Type Selection */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Request Type</h4>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="requestType"
                  value="return"
                  checked={requestType === 'return'}
                  onChange={(e) => setRequestType(e.target.value as 'return')}
                  className="mr-2 h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                />
                <span className="text-sm font-medium text-gray-700">Return Only</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="requestType"
                  value="pdd"
                  checked={requestType === 'pdd'}
                  onChange={(e) => setRequestType(e.target.value as 'pdd')}
                  className="mr-2 h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                />
                <span className="text-sm font-medium text-gray-700">PDD Only</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="requestType"
                  value="both"
                  checked={requestType === 'both'}
                  onChange={(e) => setRequestType(e.target.value as 'both')}
                  className="mr-2 h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                />
                <span className="text-sm font-medium text-gray-700">Both Return & PDD</span>
              </label>
            </div>
          </div>

          {/* Global PDD Section */}
          {(requestType === 'pdd' || requestType === 'both') && (
            <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
              <h4 className="text-sm font-medium text-orange-800 mb-3">Apply PDD to All Items</h4>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-orange-700">PDD Percentage:</label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    value={globalPDDPercentage}
                    onChange={(e) => setGlobalPDDPercentage(parseFloat(e.target.value) || 0)}
                    className="w-20 px-2 py-1 text-sm border border-orange-300 rounded focus:ring-orange-500 focus:border-orange-500"
                    placeholder="0.00"
                  />
                  <span className="text-sm text-orange-700">%</span>
                </div>
                <button
                  type="button"
                  onClick={() => {
                    if (globalPDDPercentage > 0) {
                      const updatedItems = items.map((item, index) => {
                        const originalItem = salesOrder.items[index];
                        const pddAmount = (originalItem.total_price * globalPDDPercentage) / 100;
                        return {
                          ...item,
                          pdd_percentage: globalPDDPercentage,
                          pdd_amount: Math.round(pddAmount * 100) / 100
                        };
                      });
                      setItems(updatedItems);
                      toast.success(`Applied ${globalPDDPercentage}% PDD to all items`);
                    }
                  }}
                  className="px-3 py-1 bg-orange-600 text-white rounded text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500"
                >
                  Apply to All
                </button>
                <button
                  type="button"
                  onClick={() => {
                    const updatedItems = items.map(item => ({
                      ...item,
                      pdd_percentage: 0,
                      pdd_amount: 0
                    }));
                    setItems(updatedItems);
                    setGlobalPDDPercentage(0);
                    toast.success('Cleared PDD from all items');
                  }}
                  className="px-3 py-1 bg-gray-500 text-white rounded text-sm font-medium hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Clear All
                </button>
              </div>
              <p className="text-xs text-orange-600 mt-2">
                Enter a percentage and click "Apply to All" to set the same PDD percentage for all items
              </p>
            </div>
          )}

          {/* Items Table */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Item Details</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Original Qty
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Unit Price
                    </th>
                    {(requestType === 'return' || requestType === 'both') && (
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Return Qty
                      </th>
                    )}
                    {(requestType === 'pdd' || requestType === 'both') && (
                      <>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          PDD %
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          PDD Amount
                        </th>
                      </>
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {salesOrder.items.map((item, index) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{item.product_name}</div>
                          <div className="text-sm text-gray-500">{item.sku_code}</div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {item.quantity} {item.unit_type === 'box' ? 'boxes' : 'kg'}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        ₹{Number(item.unit_price || 0).toFixed(2)}
                      </td>
                      {(requestType === 'return' || requestType === 'both') && (
                        <td className="px-4 py-4">
                          <div className="flex items-center space-x-2">
                            <button
                              type="button"
                              onClick={() => updateItem(index, 'return_quantity', Math.max(0, items[index]?.return_quantity - 1))}
                              className="p-1 text-gray-400 hover:text-gray-600"
                            >
                              <Minus className="h-4 w-4" />
                            </button>
                            <input
                              type="number"
                              min="0"
                              max={item.quantity}
                              value={items[index]?.return_quantity || 0}
                              onChange={(e) => updateItem(index, 'return_quantity', Math.min(item.quantity, Math.max(0, parseInt(e.target.value) || 0)))}
                              className="w-16 px-2 py-1 text-sm border border-gray-300 rounded text-center"
                            />
                            <button
                              type="button"
                              onClick={() => updateItem(index, 'return_quantity', Math.min(item.quantity, items[index]?.return_quantity + 1))}
                              className="p-1 text-gray-400 hover:text-gray-600"
                            >
                              <Plus className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      )}
                      {(requestType === 'pdd' || requestType === 'both') && (
                        <>
                          <td className="px-4 py-4">
                            <input
                              type="number"
                              min="0"
                              max="100"
                              step="0.01"
                              value={items[index]?.pdd_percentage || 0}
                              onChange={(e) => updateItem(index, 'pdd_percentage', Math.max(0, parseFloat(e.target.value) || 0))}
                              className="w-20 px-2 py-1 text-sm border border-gray-300 rounded"
                              placeholder="0.00"
                            />
                          </td>
                          <td className="px-4 py-4">
                            <input
                              type="number"
                              min="0"
                              step="0.01"
                              value={items[index]?.pdd_amount || 0}
                              onChange={(e) => updateItem(index, 'pdd_amount', Math.max(0, parseFloat(e.target.value) || 0))}
                              className="w-24 px-2 py-1 text-sm border border-gray-300 rounded"
                              placeholder="0.00"
                            />
                          </td>
                        </>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Summary */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-sm font-medium text-blue-800 mb-3 flex items-center">
              <Calculator className="h-4 w-4 mr-2" />
              Summary
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-blue-700">Original Order Value:</span>
                <span className="ml-2 font-medium text-blue-900">₹{salesOrder.total_amount.toLocaleString()}</span>
              </div>
              {(requestType === 'return' || requestType === 'both') && (
                <div>
                  <span className="text-blue-700">Total Return Value:</span>
                  <span className="ml-2 font-medium text-blue-900">₹{getTotalReturnValue().toLocaleString()}</span>
                </div>
              )}
              {(requestType === 'pdd' || requestType === 'both') && (
                <div>
                  <span className="text-blue-700">Total PDD Amount:</span>
                  <span className="ml-2 font-medium text-blue-900">₹{getTotalPDDAmount().toLocaleString()}</span>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes (Optional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Enter any additional notes or reasons for the return/PDD request..."
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isProcessing}
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleSubmit}
              disabled={isProcessing || !hasValidItems()}
              className="px-4 py-2 bg-orange-600 text-white rounded-md text-sm font-medium hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              {isProcessing ? 'Submitting...' : 'Submit Request'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReturnPDDModal;
