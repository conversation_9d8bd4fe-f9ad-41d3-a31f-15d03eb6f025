import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, Edit3 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { inventoryService } from '../../services/api/inventoryService';

interface InventoryItem {
  id: string;
  productId: string;
  productName: string;
  skuId: string;
  skuCode: string;
  unitType: 'box' | 'loose';
  currentQuantity: number;
  currentWeight: number;
}

interface UpdateSKUModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: InventoryItem | null;
  onSuccess: () => void;
}

const UPDATE_REASONS = [
  'SKU code correction',
  'Unit type change',
  'Weight specification update',
  'Product standardization',
  'System migration',
  'Other'
];

const UpdateSKUModal: React.FC<UpdateSKUModalProps> = ({
  isOpen,
  onClose,
  item,
  onSuccess
}) => {
  const [newProductName, setNewProductName] = useState<string>('');
  const [newSkuCode, setNewSkuCode] = useState<string>('');
  const [newUnitType, setNewUnitType] = useState<'box' | 'loose'>('box');
  const [newUnitWeight, setNewUnitWeight] = useState<number>(0);
  const [reason, setReason] = useState<string>('');
  const [customReason, setCustomReason] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Initialize form with current values when item changes
  useEffect(() => {
    if (item) {
      setNewProductName(item.productName);
      setNewSkuCode(item.skuCode);
      setNewUnitType(item.unitType);
      setNewUnitWeight(0); // We don't have unit weight in the current item structure
    }
  }, [item]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!item || !reason) {
      toast.error('Please fill in all required fields');
      return;
    }

    const finalReason = reason === 'Other' ? customReason : reason;
    if (!finalReason) {
      toast.error('Please provide a reason for the update');
      return;
    }

    // Check if any changes were made
    const hasChanges = 
      newProductName !== item.productName ||
      newSkuCode !== item.skuCode ||
      newUnitType !== item.unitType;

    if (!hasChanges) {
      toast.error('No changes detected. Please modify at least one field.');
      return;
    }

    setLoading(true);
    
    try {
      const updateData: any = {
        current_product_id: item.productId,
        current_sku_id: item.skuId,
        reason: finalReason
      };

      // Only include changed fields
      if (newProductName !== item.productName) {
        updateData.new_product_name = newProductName;
      }
      if (newSkuCode !== item.skuCode) {
        updateData.new_sku_code = newSkuCode;
      }
      if (newUnitType !== item.unitType) {
        updateData.new_unit_type = newUnitType;
      }

      await inventoryService.updateSKU(updateData);

      toast.success('Product & SKU details updated successfully');
      onSuccess();
      handleClose();
    } catch (error) {
      console.error('Error updating SKU details:', error);
      toast.error('Failed to update SKU details');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (item) {
      setNewProductName(item.productName);
      setNewSkuCode(item.skuCode);
      setNewUnitType(item.unitType);
      setNewUnitWeight(0);
    }
    setReason('');
    setCustomReason('');
    onClose();
  };

  const getChangeSummary = () => {
    if (!item) return [];
    
    const changes = [];
    
    if (newProductName !== item.productName) {
      changes.push(`Product Name: ${item.productName} → ${newProductName}`);
    }
    if (newSkuCode !== item.skuCode) {
      changes.push(`SKU Code: ${item.skuCode} → ${newSkuCode}`);
    }
    if (newUnitType !== item.unitType) {
      changes.push(`Unit Type: ${item.unitType} → ${newUnitType}`);
    }
    
    return changes;
  };

  if (!isOpen || !item) return null;

  const changes = getChangeSummary();

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-purple-100 text-purple-600">
                <Edit3 className="h-5 w-5" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">Update Product & SKU</h3>
                <p className="text-sm text-gray-500">
                  {item.productName} - {item.skuCode}
                </p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="px-6 py-4 space-y-4">
          {/* Current Details Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
            <div className="flex items-center mb-2">
              <AlertTriangle className="h-4 w-4 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-800">Current Details</span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-1 text-sm text-blue-700">
              <div>Product Name: {item.productName}</div>
              <div>SKU Code: {item.skuCode}</div>
              <div>Unit Type: {item.unitType === 'box' ? 'Box/Crate' : 'Loose'}</div>
              <div>Current Stock: {item.currentQuantity} {item.unitType === 'box' ? 'boxes' : 'kg'}</div>
            </div>
          </div>

          {/* Product Name and SKU Code - Side by Side */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Product Name
              </label>
              <input
                type="text"
                value={newProductName}
                onChange={(e) => setNewProductName(e.target.value.trim())}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter product name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                SKU Code
              </label>
              <input
                type="text"
                value={newSkuCode}
                onChange={(e) => setNewSkuCode(e.target.value.trim())}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter SKU code"
              />
            </div>
          </div>

          {/* Unit Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Unit Type
            </label>
            <div className="grid grid-cols-2 gap-3">
              <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  type="radio"
                  name="unitType"
                  value="box"
                  checked={newUnitType === 'box'}
                  onChange={(e) => setNewUnitType(e.target.value as 'box' | 'loose')}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                />
                <div className="ml-3">
                  <div className="text-sm font-medium text-gray-900">Box/Crate</div>
                </div>
              </label>
              <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  type="radio"
                  name="unitType"
                  value="loose"
                  checked={newUnitType === 'loose'}
                  onChange={(e) => setNewUnitType(e.target.value as 'box' | 'loose')}
                  className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300"
                />
                <div className="ml-3">
                  <div className="text-sm font-medium text-gray-900">Loose</div>
                </div>
              </label>
            </div>
          </div>

          {/* Reason Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reason for Update *
            </label>
            <select
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              required
            >
              <option value="">Select reason</option>
              {UPDATE_REASONS.map((r) => (
                <option key={r} value={r}>
                  {r}
                </option>
              ))}
            </select>
          </div>

          {/* Custom Reason Input */}
          {reason === 'Other' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Custom Reason *
              </label>
              <input
                type="text"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter custom reason"
                required
              />
            </div>
          )}

          {/* Changes Preview and Warnings - Combined */}
          {changes.length > 0 && (
            <div className="space-y-3">
              {/* Changes Preview */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                <div className="text-sm font-medium text-yellow-800 mb-1">Changes to be Applied</div>
                <div className="text-sm text-yellow-700">
                  {changes.map((change, index) => (
                    <div key={index}>• {change}</div>
                  ))}
                </div>
              </div>

              {/* Combined Warnings */}
              <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="h-4 w-4 text-orange-600 mr-2" />
                  <span className="text-sm font-medium text-orange-800">Important Warnings</span>
                </div>
                <div className="text-sm text-orange-700 space-y-1">
                  <div>• Changes affect ALL transactions (past and future) for this SKU</div>
                  <div>• Current inventory quantities will remain unchanged</div>
                  {newUnitType !== item.unitType && (
                    <div>• Unit type change will affect how inventory is counted and measured</div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || !reason || (reason === 'Other' && !customReason) || changes.length === 0}
              className="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Updating...' : 'Update SKU'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateSKUModal;
