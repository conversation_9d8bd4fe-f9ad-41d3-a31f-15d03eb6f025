import { apiRequest } from './config';

export const customerService = {
  // Get all customers
  getAll: async () => {
    return await apiRequest('/api/customers');
  },

  // Get single customer
  getById: async (id: string) => {
    return await apiRequest(`/api/customers/${id}`);
  },

  // Create customer
  create: async (customerData: any) => {
    return await apiRequest('/api/customers', {
      method: 'POST',
      body: JSON.stringify(customerData),
    });
  },

  // Update customer
  update: async (id: string, customerData: any) => {
    return await apiRequest(`/api/customers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(customerData),
    });
  },

  // Delete customer
  delete: async (id: string) => {
    return await apiRequest(`/api/customers/${id}`, {
      method: 'DELETE',
    });
  },
};
