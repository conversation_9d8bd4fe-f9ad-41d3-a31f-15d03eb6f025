import { apiRequest } from './config';

export const purchaseRecordService = {
  // Get all purchase records
  getAll: async (filters?: { startDate?: string; endDate?: string }) => {
    const queryParams = new URLSearchParams();
    if (filters?.startDate) queryParams.append('startDate', filters.startDate);
    if (filters?.endDate) queryParams.append('endDate', filters.endDate);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/purchase-records?${queryString}` : '/api/purchase-records';
    
    return await apiRequest(url);
  },

  // Get single purchase record
  getById: async (id: string) => {
    return await apiRequest(`/api/purchase-records/${id}`);
  },

  // Create purchase record
  create: async (recordData: any, itemsData: any[], additionalCostsData: any[]) => {
    return await apiRequest('/api/purchase-records', {
      method: 'POST',
      body: JSON.stringify({
        recordData,
        itemsData,
        additionalCostsData
      }),
    });
  },

  // Update purchase record
  update: async (id: string, recordData: any, itemsData?: any[], additionalCostsData?: any[]) => {
    return await apiRequest(`/api/purchase-records/${id}`, {
      method: 'PUT',
      body: JSON.stringify({
        recordData,
        itemsData,
        additionalCostsData
      }),
    });
  },

  // Update status
  updateStatus: async (id: string, status: string, closureNotes?: string) => {
    return await apiRequest(`/api/purchase-records/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status, closureNotes }),
    });
  },

  // Delete purchase record
  delete: async (id: string) => {
    return await apiRequest(`/api/purchase-records/${id}`, {
      method: 'DELETE',
    });
  },

  // Update closure status
  updateClosureStatus: async (id: string, status: string, notes?: string) => {
    return await apiRequest(`/api/purchase-records/${id}/closure-status`, {
      method: 'PUT',
      body: JSON.stringify({ status, notes }),
    });
  },
};
