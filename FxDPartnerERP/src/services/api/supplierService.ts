import { apiRequest } from './config';

export const supplierService = {
  // Get all suppliers
  getAll: async () => {
    return await apiRequest('/api/suppliers');
  },

  // Get single supplier
  getById: async (id: string) => {
    return await apiRequest(`/api/suppliers/${id}`);
  },

  // Create supplier
  create: async (data: any) => {
    return await apiRequest('/api/suppliers', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update supplier
  update: async (id: string, data: any) => {
    return await apiRequest(`/api/suppliers/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Delete supplier
  delete: async (id: string) => {
    return await apiRequest(`/api/suppliers/${id}`, {
      method: 'DELETE',
    });
  },

  // Get purchase records for a supplier
  getPurchaseRecords: async (id: string) => {
    return await apiRequest(`/api/suppliers/${id}/purchase-records`);
  },

  // Get payments for a supplier
  getPayments: async (id: string) => {
    return await apiRequest(`/api/suppliers/${id}/payments`);
  },

  // Update supplier balance
  updateBalance: async (id: string, data: { amount: number; operation: 'add' | 'subtract' }) => {
    return await apiRequest(`/api/suppliers/${id}/balance`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Get suppliers with statistics
  getWithStats: async () => {
    return await apiRequest('/api/suppliers/stats');
  },

  // Search suppliers
  search: async (query: string) => {
    return await apiRequest(`/api/suppliers/search?q=${encodeURIComponent(query)}`);
  },
};
