import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: `${API_BASE_URL}/api/documents`,
  timeout: 30000, // 30 seconds for file uploads
});

// Add request interceptor to include auth token if available
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  
  const organizationId = localStorage.getItem('organizationId');
  if (organizationId) {
    config.headers['x-organization-id'] = organizationId;
  }
  
  return config;
});

export interface FileUploadResult {
  fileKey: string;
  fileUrl: string;
  fileName: string;
  fileSize: number;
  contentType: string;
}

export interface DocumentData {
  id: string;
  file_name: string;
  file_key: string;
  file_url?: string;
  file_size: number;
  content_type: string;
  entity_type: string;
  entity_id: string;
  display_name?: string;
  organization_id: string;
  uploaded_by: string;
  created_at: string;
  updated_at: string;
  uploader?: {
    id: string;
    first_name: string;
    email: string;
  };
}

export interface AttachDocumentData {
  fileKey: string;
  fileName: string;
  fileSize: number;
  contentType: string;
  displayName?: string;
}

export class DocumentService {
  /**
   * Upload a single file
   */
  static async uploadFile(file: File): Promise<FileUploadResult> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          console.log(`Upload progress: ${percentCompleted}%`);
        }
      },
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to upload file');
    }
  }

  /**
   * Upload multiple files
   */
  static async uploadMultipleFiles(files: File[]): Promise<FileUploadResult[]> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    const response = await apiClient.post('/upload/multiple', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          console.log(`Upload progress: ${percentCompleted}%`);
        }
      },
    });

    if (response.data.success) {
      return response.data.data.files;
    } else {
      throw new Error(response.data.message || 'Failed to upload files');
    }
  }

  /**
   * Attach a document to an entity
   */
  static async attachDocument(
    entityType: string,
    entityId: string,
    documentData: AttachDocumentData
  ): Promise<DocumentData> {
    const response = await apiClient.post(`/${entityType}/${entityId}`, documentData);

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to attach document');
    }
  }

  /**
   * Get documents for an entity
   */
  static async getEntityDocuments(entityType: string, entityId: string): Promise<DocumentData[]> {
    const response = await apiClient.get(`/${entityType}/${entityId}`);

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to fetch documents');
    }
  }

  /**
   * Delete a document
   */
  static async deleteDocument(documentId: string): Promise<void> {
    const response = await apiClient.delete(`/${documentId}`);

    if (!response.data.success) {
      throw new Error(response.data.message || 'Failed to delete document');
    }
  }

  /**
   * Search documents
   */
  static async searchDocuments(searchTerm: string): Promise<DocumentData[]> {
    const response = await apiClient.get('/search', {
      params: { q: searchTerm }
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to search documents');
    }
  }

  /**
   * Get organization documents
   */
  static async getOrganizationDocuments(limit?: number): Promise<DocumentData[]> {
    const response = await apiClient.get('/organization/all', {
      params: limit ? { limit } : {}
    });

    if (response.data.success) {
      return response.data.data;
    } else {
      throw new Error(response.data.message || 'Failed to fetch organization documents');
    }
  }

  /**
   * Upload file and attach to entity in one step
   */
  static async uploadAndAttach(
    file: File,
    entityType: string,
    entityId: string,
    displayName?: string
  ): Promise<DocumentData> {
    // First upload the file
    const uploadResult = await this.uploadFile(file);

    // Then attach it to the entity
    const attachData: AttachDocumentData = {
      fileKey: uploadResult.fileKey,
      fileName: uploadResult.fileName,
      fileSize: uploadResult.fileSize,
      contentType: uploadResult.contentType,
      displayName: displayName || uploadResult.fileName
    };

    return await this.attachDocument(entityType, entityId, attachData);
  }

  /**
   * Upload multiple files and attach to entity
   */
  static async uploadMultipleAndAttach(
    files: File[],
    entityType: string,
    entityId: string
  ): Promise<DocumentData[]> {
    // First upload all files
    const uploadResults = await this.uploadMultipleFiles(files);

    // Then attach each to the entity
    const attachPromises = uploadResults.map(result => {
      const attachData: AttachDocumentData = {
        fileKey: result.fileKey,
        fileName: result.fileName,
        fileSize: result.fileSize,
        contentType: result.contentType,
        displayName: result.fileName
      };
      return this.attachDocument(entityType, entityId, attachData);
    });

    return await Promise.all(attachPromises);
  }

  /**
   * Check if file type is supported
   */
  static isSupportedFileType(file: File): boolean {
    const supportedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'application/pdf',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/csv'
    ];

    return supportedTypes.includes(file.type);
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file icon based on content type
   */
  static getFileIcon(contentType: string): string {
    if (contentType.startsWith('image/')) {
      return '🖼️';
    } else if (contentType === 'application/pdf') {
      return '📄';
    } else if (contentType.includes('excel') || contentType.includes('spreadsheet')) {
      return '📊';
    } else if (contentType.includes('word') || contentType.includes('document')) {
      return '📝';
    } else if (contentType.startsWith('text/')) {
      return '📄';
    } else {
      return '📎';
    }
  }
}

export default DocumentService;
