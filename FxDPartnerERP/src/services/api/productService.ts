import { apiRequest } from './config';

export const productService = {
  // Get all products
  getAll: async () => {
    return await apiRequest('/api/products');
  },

  // Get single product
  getById: async (id: string) => {
    return await apiRequest(`/api/products/${id}`);
  },

  // Create product
  create: async (data: any) => {
    return await apiRequest('/api/products', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update product
  update: async (id: string, data: any) => {
    return await apiRequest(`/api/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Delete product
  delete: async (id: string) => {
    return await apiRequest(`/api/products/${id}`, {
      method: 'DELETE',
    });
  },
};

export const skuService = {
  // Get all SKUs
  getAll: async () => {
    return await apiRequest('/api/products/skus');
  },

  // Get SKUs by product
  getByProduct: async (productId: string) => {
    return await apiRequest(`/api/products/${productId}/skus`);
  },

  // Create SKU
  create: async (data: any) => {
    return await apiRequest('/api/products/skus', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update SKU
  update: async (id: string, data: any) => {
    return await apiRequest(`/api/products/skus/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Delete SKU
  delete: async (id: string) => {
    return await apiRequest(`/api/products/skus/${id}`, {
      method: 'DELETE',
    });
  },
};
