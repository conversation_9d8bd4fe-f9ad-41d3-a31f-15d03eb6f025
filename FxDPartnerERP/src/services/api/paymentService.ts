import { apiRequest } from './config';

export const paymentService = {
  // Get all payments
  getAll: async () => {
    return await apiRequest('/api/payments');
  },

  // Get single payment
  getById: async (id: string) => {
    return await apiRequest(`/api/payments/${id}`);
  },

  // Create payment
  create: async (paymentData: any, proofFile?: File) => {
    console.log('=== PAYMENT SERVICE DEBUG ===');
    console.log('Payment data received:', paymentData);
    console.log('Proof file:', proofFile);
    
    const formData = new FormData();
    const jsonString = JSON.stringify(paymentData);
    console.log('JSON string being sent:', jsonString);
    formData.append('data', jsonString);
    
    if (proofFile) {
      formData.append('proof', proofFile);
      console.log('Proof file attached:', proofFile.name, proofFile.size);
    }

    console.log('FormData entries:');
    for (let [key, value] of formData.entries()) {
      console.log(`${key}:`, value);
    }
    console.log('=== END PAYMENT SERVICE DEBUG ===');

    return await apiRequest('/api/payments', {
      method: 'POST',
      body: formData,
      // Don't set any headers - the config will detect FormData and skip Content-Type
    });
  },

  // Update payment
  update: async (id: string, paymentData: any, proofFile?: File) => {
    const formData = new FormData();
    formData.append('data', JSON.stringify(paymentData));
    
    if (proofFile) {
      formData.append('proof', proofFile);
    }

    return await apiRequest(`/api/payments/${id}`, {
      method: 'PUT',
      body: formData,
      headers: {},
    });
  },

  // Delete payment
  delete: async (id: string) => {
    return await apiRequest(`/api/payments/${id}`, {
      method: 'DELETE',
    });
  },

  // Get payments by customer
  getByCustomer: async (customerId: string) => {
    return await apiRequest(`/api/payments/party/${customerId}`);
  },

  // Get payments by supplier
  getBySupplier: async (supplierId: string) => {
    return await apiRequest(`/api/payments/party/${supplierId}`);
  },

  // Get payments by party (unified method for both customers and suppliers)
  getByParty: async (partyId: string) => {
    return await apiRequest(`/api/payments/party/${partyId}`);
  },

  // Get payments by reference (e.g., sales order)
  getByReference: async (referenceId: string) => {
    return await apiRequest(`/api/payments/reference/${referenceId}`);
  },
};
