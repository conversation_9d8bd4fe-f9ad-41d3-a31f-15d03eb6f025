import { apiRequest } from './config';

export const grnService = {
  // Mark sales order as GRN Complete
  markGRNComplete: async (salesOrderId: string) => {
    return await apiRequest(`/api/grn/sales-orders/${salesOrderId}/grn-complete`, {
      method: 'PATCH',
    });
  },

  // Create Return/PDD request
  createReturnPDDRequest: async (data: {
    sales_order_id: string;
    request_type: 'return' | 'pdd' | 'both';
    items: Array<{
      sales_order_item_id: string;
      original_quantity: number;
      return_quantity?: number;
      pdd_percentage?: number;
      pdd_amount?: number;
    }>;
    notes?: string;
  }) => {
    return await apiRequest('/api/grn/return-pdd-requests', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Get pending GRN approvals
  getPendingApprovals: async () => {
    return await apiRequest('/api/grn/pending-approvals');
  },

  // Approve GRN request
  approveRequest: async (requestId: string) => {
    return await apiRequest(`/api/grn/requests/${requestId}/approve`, {
      method: 'PATCH',
    });
  },

  // Reject GRN request
  rejectRequest: async (requestId: string, rejectionReason: string) => {
    return await apiRequest(`/api/grn/requests/${requestId}/reject`, {
      method: 'PATCH',
      body: JSON.stringify({ rejection_reason: rejectionReason }),
    });
  },

  // Get GRN history for a sales order
  getHistory: async (salesOrderId: string) => {
    return await apiRequest(`/api/grn/sales-orders/${salesOrderId}/history`);
  },
};
