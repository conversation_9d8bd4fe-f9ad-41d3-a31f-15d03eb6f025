import { apiRequest } from './config';

export const vehicleArrivalService = {
  // Get all vehicle arrivals
  getAll: async () => {
    return await apiRequest('/api/procurement/vehicle-arrivals');
  },

  // Get single vehicle arrival
  getById: async (id: string) => {
    return await apiRequest(`/api/procurement/vehicle-arrivals/${id}`);
  },

  // Create vehicle arrival
  create: async (data: any) => {
    return await apiRequest('/api/procurement/vehicle-arrivals', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update vehicle arrival
  update: async (id: string, data: any) => {
    return await apiRequest(`/api/procurement/vehicle-arrivals/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Update status
  updateStatus: async (id: string, status: string, notes?: string, finalQuantities?: any[]) => {
    return await apiRequest(`/api/procurement/vehicle-arrivals/${id}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ 
        status, 
        notes, 
        final_quantities: finalQuantities 
      }),
    });
  },

  // Delete vehicle arrival
  delete: async (id: string) => {
    return await apiRequest(`/api/procurement/vehicle-arrivals/${id}`, {
      method: 'DELETE',
    });
  },
};
