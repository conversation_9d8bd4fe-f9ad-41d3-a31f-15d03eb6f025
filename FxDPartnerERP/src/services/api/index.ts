// Main API services export
export { customerService } from './customerService';
export { supplierService } from './supplierService';
export { salesService } from './salesService';
export { productService } from './productService';
export { inventoryService } from './inventoryService';
export { paymentService } from './paymentService';
export { purchaseRecordService } from './purchaseRecordService';
export { vehicleArrivalService } from './vehicleArrivalService';
export { attachmentService } from './attachmentService';
export { API_CONFIG, apiRequest } from './config';

// Import services for backward compatibility functions
import { customerService } from './customerService';
import { supplierService } from './supplierService';
import { salesService } from './salesService';
import { productService } from './productService';
import { inventoryService } from './inventoryService';
import { paymentService } from './paymentService';
import { purchaseRecordService } from './purchaseRecordService';
import { vehicleArrivalService } from './vehicleArrivalService';
import { attachmentService } from './attachmentService';

// Backward compatibility wrapper functions for old API
// These functions maintain the old function names while using the new service structure

// Sales functions
export const getSalesOrders = () => salesService.getAll();
export const getSalesOrder = (id: string) => salesService.getById(id);
export const getSalesOrdersByCustomerId = (customerId: string) => salesService.getByCustomer(customerId);
export const createSalesOrder = (data: any) => salesService.create(data);
export const createSalesOrderWithMultiplePayments = (orderData: any, paymentMethods: any[]) => 
  salesService.createWithPayments(orderData, paymentMethods);
export const updateSalesOrder = (id: string, data: any) => salesService.update(id, data);
export const deleteSalesOrder = (id: string) => salesService.delete(id);
export const cancelSalesOrder = (id: string) => salesService.cancel(id);
export const approvePendingSalesOrder = (id: string, newStatus?: string) => salesService.approve(id, newStatus);
export const rejectPendingSalesOrder = (id: string, rejectionReason: string) => 
  salesService.reject(id, rejectionReason);
export const updateSalesOrderDispatchDetails = (id: string, data: any) => salesService.updateDispatch(id, data);
export const getOutstationSalesOrders = () => salesService.getOutstation();
export const checkInventoryForSalesOrder = (items: any[]) => salesService.checkInventory(items);
export const getSalesAnalytics = () => salesService.getAnalytics();

// Customer functions
export const getCustomers = () => customerService.getAll();
export const getCustomer = (id: string) => customerService.getById(id);
export const createCustomer = (data: any) => customerService.create(data);
export const updateCustomer = (id: string, data: any) => customerService.update(id, data);
export const deleteCustomer = (id: string) => customerService.delete(id);
export const updateCustomerBalance = (customerId: string, amount: number, operation: 'add' | 'subtract') =>
  Promise.resolve({ success: true }); // Placeholder - implement in backend
export const getCustomerCreditExtensions = (customerId: string) => Promise.resolve([]); // Placeholder

// Supplier functions
export const getSuppliers = () => supplierService.getAll();
export const getSupplier = (id: string) => supplierService.getById(id);
export const createSupplier = (data: any) => supplierService.create(data);
export const updateSupplier = (id: string, data: any) => supplierService.update(id, data);
export const deleteSupplier = (id: string) => supplierService.delete(id);
export const updateSupplierBalance = (supplierId: string, amount: number, operation: 'add' | 'subtract') =>
  Promise.resolve({ success: true }); // Placeholder - implement in backend
export const getPurchaseRecordsBySupplierId = (supplierId: string) => Promise.resolve([]); // Placeholder

// Product functions
export const getProducts = () => productService.getAll();
export const getProduct = (id: string) => productService.getById(id);
export const createProduct = (data: any) => productService.create(data);
export const updateProduct = (id: string, data: any) => productService.update(id, data);
export const deleteProduct = (id: string) => productService.delete(id);
export const getAllProductsAndSKUs = () => productService.getAll(); // Use getAll for now
export const createSKU = (data: any) => Promise.resolve(data); // Placeholder

// Inventory functions
export const getAllInventory = () => inventoryService.getCurrent();
export const getAvailableInventory = () => inventoryService.getAvailable();
export const adjustInventoryAsAnotherSKU = (currentProductId: string, currentSkuId: string, newProductId: string, newSkuId: string, reason: string) =>
  inventoryService.adjustAsAnotherSKU(currentProductId, currentSkuId, newProductId, newSkuId, reason);
export const adjustInventoryFromAnotherSource = (productId: string, skuId: string, source: string, reason: string) =>
  inventoryService.adjustFromAnotherSource(productId, skuId, source, reason);

// Payment functions
export const getPayments = () => paymentService.getAll();
export const getPayment = (id: string) => paymentService.getById(id);
export const getPaymentsByPartyId = (partyId: string) => {
  // Try both customer and supplier endpoints
  return paymentService.getByCustomer(partyId).catch(() => paymentService.getBySupplier(partyId));
};
export const createPayment = (data: any, proofFile?: File) => paymentService.create(data, proofFile);
export const updatePayment = (id: string, data: any) => paymentService.update(id, data);
export const deletePayment = (id: string) => paymentService.delete(id);
export const uploadPaymentProof = (file: File) => Promise.resolve(''); // Placeholder

// Purchase Record functions
export const getPurchaseRecords = () => purchaseRecordService.getAll();
export const getPurchaseRecord = (id: string) => purchaseRecordService.getById(id);
export const createPurchaseRecord = (record: any, items: any[], costs: any[]) => 
  purchaseRecordService.create(record, items, costs);
export const updatePurchaseRecord = (id: string, record: any, items?: any[], costs?: any[]) =>
  purchaseRecordService.update(id, record, items, costs);
export const deletePurchaseRecord = (id: string) => purchaseRecordService.delete(id);
export const updatePurchaseRecordClosureStatus = (id: string, status: string, closureNotes?: string) =>
  purchaseRecordService.updateClosureStatus(id, status, closureNotes);

// Vehicle Arrival functions
export const getVehicleArrivals = () => vehicleArrivalService.getAll();
export const getVehicleArrival = (id: string) => vehicleArrivalService.getById(id);
export const createVehicleArrival = (arrival: any, items?: any[], attachments?: any[]) =>
  vehicleArrivalService.create(arrival);
export const updateVehicleArrival = (id: string, arrival: any, items?: any[], attachments?: any[]) =>
  vehicleArrivalService.update(id, arrival);
export const updateVehicleArrivalStatus = (id: string, status: string, updates?: any, finalQuantities?: any[]) =>
  vehicleArrivalService.updateStatus(id, status);

// Attachment functions
export const uploadAttachment = (file: File) => attachmentService.upload(file, 'general');

// Sales Order Payment functions (for multiple payment system)
export const createSalesOrderPayment = (payment: any) => Promise.resolve(payment); // Placeholder
export const getSalesOrderPayments = (salesOrderId: string) => Promise.resolve([]); // Placeholder
export const updateSalesOrderPayment = (id: string, payment: any) => Promise.resolve(payment); // Placeholder
export const createCustomerCreditExtension = (extension: any) => Promise.resolve(extension); // Placeholder

// Export types
export type {
  Customer,
  Supplier,
  CreateCustomerData,
  CreateSupplierData,
  UpdateCustomerData,
  UpdateSupplierData,
  SalesOrder,
  SalesOrderItem,
  PurchaseRecord,
  PurchaseRecordItem,
  PurchaseRecordCost,
  Payment,
  ApiResponse,
  BalanceUpdateRequest,
} from './types';
