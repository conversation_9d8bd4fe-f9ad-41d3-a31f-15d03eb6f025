import { apiRequest } from './config';

export const salesService = {
  // Get all sales orders
  getAll: async () => {
    return await apiRequest('/api/sales');
  },

  // Get single sales order
  getById: async (id: string) => {
    return await apiRequest(`/api/sales/${id}`);
  },

  // Get sales orders by customer
  getByCustomer: async (customerId: string) => {
    return await apiRequest(`/api/sales/customer/${customerId}`);
  },

  // Create sales order
  create: async (data: any) => {
    return await apiRequest('/api/sales', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Create sales order with multiple payments
  createWithPayments: async (orderData: any, paymentMethods: any[]) => {
    return await apiRequest('/api/sales/with-payments', {
      method: 'POST',
      body: JSON.stringify({ orderData, paymentMethods }),
    });
  },

  // Update sales order
  update: async (id: string, data: any) => {
    return await apiRequest(`/api/sales/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Cancel sales order
  cancel: async (id: string) => {
    return await apiRequest(`/api/sales/${id}/cancel`, {
      method: 'PATCH',
    });
  },

  // Update dispatch details
  updateDispatch: async (id: string, data: any) => {
    return await apiRequest(`/api/sales/${id}/dispatch`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  },

  // Get outstation sales orders
  getOutstation: async () => {
    return await apiRequest('/api/sales/outstation');
  },

  // Approve pending sales order
  approve: async (id: string, newStatus?: string) => {
    return await apiRequest(`/api/sales/${id}/approve`, {
      method: 'PATCH',
      body: JSON.stringify({ newStatus }),
    });
  },

  // Reject pending sales order
  reject: async (id: string, rejectionReason: string) => {
    return await apiRequest(`/api/sales/${id}/reject`, {
      method: 'PATCH',
      body: JSON.stringify({ rejectionReason }),
    });
  },

  // Check inventory for sales order
  checkInventory: async (items: any[]) => {
    return await apiRequest('/api/sales/check-inventory', {
      method: 'POST',
      body: JSON.stringify({ items }),
    });
  },


  // Delete sales order
  delete: async (id: string) => {
    return await apiRequest(`/api/sales/${id}`, {
      method: 'DELETE',
    });
  },

  // Get sales analytics
  getAnalytics: async () => {
    return await apiRequest('/api/sales/analytics');
  },
};
