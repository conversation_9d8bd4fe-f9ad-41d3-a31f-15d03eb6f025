// API configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:9000',
  ORGANIZATION_ID: import.meta.env.VITE_ORGANIZATION_ID || 'default-org-id',
  TIMEOUT: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'), // 10 seconds
};

// Helper function to make API requests
export async function apiRequest(endpoint: string, options: RequestInit = {}) {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;
  
  // Get auth token from localStorage
  const authToken = localStorage.getItem('auth_token');
  
  const defaultHeaders: Record<string, string> = {
    'x-organization-id': API_CONFIG.ORGANIZATION_ID,
  };

  // Add authorization header if token exists
  if (authToken) {
    defaultHeaders['Authorization'] = `Bearer ${authToken}`;
  }

  // Only add Content-Type for non-FormData requests
  const isFormData = options.body instanceof FormData;
  if (!isFormData) {
    defaultHeaders['Content-Type'] = 'application/json';
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  try {
    console.log(`Making API request to: ${url}`, config);
    const response = await fetch(url, config);
    
    console.log(`API response status: ${response.status}`, response.statusText);
    
    if (!response.ok) {
      // Handle authentication errors
      if (response.status === 401) {
        // Clear invalid auth data
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        localStorage.removeItem('organization');
        // Redirect to login or trigger re-authentication
        window.location.href = '/FxDPartnerERP/login';
        throw new Error('Authentication required. Please log in again.');
      }
      
      // Try to parse error response as JSON, fallback to text
      let errorMessage = `HTTP error! status: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorData.message || errorMessage;
      } catch (parseError) {
        // If JSON parsing fails, try to get text response
        try {
          const errorText = await response.text();
          if (errorText) {
            errorMessage = `Server error: ${errorText.substring(0, 200)}...`;
          }
        } catch (textError) {
          // Keep the default error message
        }
      }
      
      throw new Error(errorMessage);
    }
    
    const data = await response.json();
    
    // Handle different response formats
    if (data.data !== undefined) {
      return data.data; // Backend returns { success: true, message: string, data: any }
    } else if (data.success !== undefined) {
      return data; // Some endpoints might return the full response
    } else {
      return data; // Direct data response
    }
  } catch (error) {
    console.error(`API request failed for ${endpoint}:`, error);
    throw error;
  }
}
