import { apiRequest } from './config';

export const inventoryService = {
  // Get current inventory
  getCurrent: async () => {
    return await apiRequest('/api/inventory');
  },

  // Get available inventory
  getAvailable: async () => {
    return await apiRequest('/api/inventory/available');
  },

  // Adjust inventory
  adjust: async (data: any) => {
    return await apiRequest('/api/inventory/adjust', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update inventory item
  update: async (id: string, data: any) => {
    return await apiRequest(`/api/inventory/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  },

  // Adjust inventory as another SKU
  adjustAsAnotherSKU: async (currentProductId: string, currentSkuId: string, newProductId: string, newSkuId: string, reason: string) => {
    return await apiRequest('/api/inventory/adjust-as-sku', {
      method: 'POST',
      body: JSON.stringify({
        current_product_id: currentProductId,
        current_sku_id: currentSkuId,
        new_product_id: newProductId,
        new_sku_id: newSkuId,
        reason
      }),
    });
  },

  // Adjust inventory from another source
  adjustFromAnotherSource: async (productId: string, skuId: string, source: string, reason: string) => {
    return await apiRequest('/api/inventory/adjust-source', {
      method: 'POST',
      body: JSON.stringify({
        product_id: productId,
        sku_id: skuId,
        source,
        reason
      }),
    });
  },
};
