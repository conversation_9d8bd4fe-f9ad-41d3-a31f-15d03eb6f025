import { apiRequest } from './config';

export const inventoryService = {
  // Get current inventory
  getCurrent: async () => {
    return await apiRequest('/api/inventory');
  },

  // Get available inventory
  getAvailable: async () => {
    return await apiRequest('/api/inventory/available');
  },

  // Adjust inventory
  adjust: async (data: any) => {
    return await apiRequest('/api/inventory/adjust', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update inventory item
  update: async (id: string, data: any) => {
    return await apiRequest(`/api/inventory/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  },

  // Adjust inventory as another SKU
  adjustAsAnotherSKU: async (currentProductId: string, currentSkuId: string, newProductId: string, newSkuId: string, reason: string) => {
    return await apiRequest('/api/inventory/adjust-as-another-sku', {
      method: 'POST',
      body: JSON.stringify({
        currentProductId,
        currentSkuId,
        newProductId,
        newSkuId,
        reason
      }),
    });
  },

  // Mark inventory as dump
  markAsDump: async (data: {
    product_id: string;
    sku_id: string;
    quantity: number;
    weight?: number;
    reason: string;
    reason_notes?: string;
  }) => {
    return await apiRequest('/api/inventory/mark-dump', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Update SKU details
  updateSKU: async (data: {
    current_product_id: string;
    current_sku_id: string;
    new_sku_code?: string;
    new_unit_type?: string;
    new_unit_weight?: number;
    reason: string;
  }) => {
    return await apiRequest('/api/inventory/update-sku', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Manual inventory adjustment
  manualAdjust: async (data: {
    product_id: string;
    sku_id: string;
    adjustment_type: 'add' | 'subtract' | 'set';
    quantity_change: number;
    weight_change?: number;
    reason: string;
    notes?: string;
  }) => {
    return await apiRequest('/api/inventory/manual-adjust', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // Get inventory dumps
  getDumps: async (filters?: {
    startDate?: string;
    endDate?: string;
    reason?: string;
    productId?: string;
    skuId?: string;
  }) => {
    const queryParams = new URLSearchParams();
    if (filters?.startDate) queryParams.append('startDate', filters.startDate);
    if (filters?.endDate) queryParams.append('endDate', filters.endDate);
    if (filters?.reason) queryParams.append('reason', filters.reason);
    if (filters?.productId) queryParams.append('productId', filters.productId);
    if (filters?.skuId) queryParams.append('skuId', filters.skuId);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/inventory/dumps?${queryString}` : '/api/inventory/dumps';
    
    return await apiRequest(url);
  },

  // Get adjustment reports
  getAdjustmentReports: async (filters?: {
    startDate?: string;
    endDate?: string;
    type?: string;
    productId?: string;
    skuId?: string;
  }) => {
    const queryParams = new URLSearchParams();
    if (filters?.startDate) queryParams.append('startDate', filters.startDate);
    if (filters?.endDate) queryParams.append('endDate', filters.endDate);
    if (filters?.type) queryParams.append('type', filters.type);
    if (filters?.productId) queryParams.append('productId', filters.productId);
    if (filters?.skuId) queryParams.append('skuId', filters.skuId);
    
    const queryString = queryParams.toString();
    const url = queryString ? `/api/inventory/adjustments/report?${queryString}` : '/api/inventory/adjustments/report';
    
    return await apiRequest(url);
  },

  // Search inventory
  search: async (query: string) => {
    return await apiRequest(`/api/inventory/search?q=${encodeURIComponent(query)}`);
  },

  // Get inventory history
  getHistory: async (productId: string, skuId: string) => {
    return await apiRequest(`/api/inventory/history/${productId}/${skuId}`);
  },
};
