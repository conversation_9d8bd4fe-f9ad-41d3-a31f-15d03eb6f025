import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Truck, Calendar, MapPin, User, Phone, Package, Eye, CheckCircle } from 'lucide-react';
import toast from 'react-hot-toast';
import { getOutstationSalesOrders, updateSalesOrderDispatchDetails } from '../../services/api';
import DispatchOrderModal from '../../components/modals/DispatchOrderModal';
import MobileTable from '../../components/ui/MobileTable';

const Dispatch = () => {
  const [orders, setOrders] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [isDispatchModalOpen, setIsDispatchModalOpen] = useState(false);

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    try {
      setLoading(true);
      const data = await getOutstationSalesOrders();
      setOrders(data || []);
    } catch (error) {
      console.error('Error loading orders:', error);
      toast.error('Failed to load dispatch orders');
    } finally {
      setLoading(false);
    }
  };

  const handleDispatchOrder = async (dispatchData: any) => {
    if (!selectedOrder) return;

    try {
      await updateSalesOrderDispatchDetails(selectedOrder.id, dispatchData);
      await loadOrders(); // Reload orders to reflect changes
      setIsDispatchModalOpen(false);
      setSelectedOrder(null);
    } catch (error) {
      console.error('Error dispatching order:', error);
      throw error; // Re-throw to let modal handle the error
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      dispatch_pending: { color: 'bg-yellow-100 text-yellow-800', label: 'Dispatch Pending' },
      dispatched: { color: 'bg-green-100 text-green-800', label: 'Dispatched' },
      completed: { color: 'bg-blue-100 text-blue-800', label: 'Completed' },
      cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || 
                  { color: 'bg-gray-100 text-gray-800', label: status };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const getPaymentStatusBadge = (status: string) => {
    const statusConfig = {
      paid: { color: 'bg-green-100 text-green-800', label: 'Paid' },
      partial: { color: 'bg-yellow-100 text-yellow-800', label: 'Partial' },
      unpaid: { color: 'bg-red-100 text-red-800', label: 'Unpaid' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || 
                  { color: 'bg-gray-100 text-gray-800', label: status };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTotalItems = (order: any) => {
    // Handle both possible field names from backend
    const items = order.items || order.sales_order_items || [];
    return items.reduce((sum: number, item: any) => sum + (item.quantity || 0), 0);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 sm:space-x-4">
          <div className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg">
            <Truck className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
          </div>
          <div className="min-w-0 flex-1">
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900 truncate">Dispatch Management</h1>
            <p className="text-sm sm:text-base text-gray-600 hidden sm:block">Manage outstation order dispatches</p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Package className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-600" />
            </div>
            <div className="ml-3 sm:ml-4 min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Pending Dispatch</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800">
                {orders.filter(order => order.status === 'dispatch_pending').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Truck className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
            </div>
            <div className="ml-3 sm:ml-4 min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Dispatched</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800">
                {orders.filter(order => order.status === 'dispatched').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-6 w-6 sm:h-8 sm:w-8 text-green-600" />
            </div>
            <div className="ml-3 sm:ml-4 min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Completed</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800">
                {orders.filter(order => order.status === 'completed').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-6 w-6 sm:h-8 sm:w-8 text-purple-600" />
            </div>
            <div className="ml-3 sm:ml-4 min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Total Orders</p>
              <p className="text-xl sm:text-2xl font-bold text-gray-800">{orders.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <MobileTable
        columns={[
          {
            key: 'order_details',
            label: 'Order Details',
            mobileLabel: 'Order',
            priority: 'high',
            render: (_, order) => (
              <div>
                <div className="text-sm font-medium text-gray-900">
                  #{order.order_number}
                </div>
                <div className="text-sm text-gray-500">
                  {formatDate(order.order_date)}
                </div>
              </div>
            )
          },
          {
            key: 'customer',
            label: 'Customer',
            priority: 'high',
            render: (_, order) => (
              <div>
                <div className="text-sm font-medium text-gray-900">
                  {order.customer?.name}
                </div>
                <div className="text-sm text-gray-500">
                  {order.customer?.customer_type}
                </div>
              </div>
            )
          },
          {
            key: 'delivery_info',
            label: 'Delivery Info',
            mobileLabel: 'Delivery',
            priority: 'medium',
            render: (_, order) => (
              <div className="text-sm text-gray-900">
                <div className="flex items-center mb-1">
                  <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                  <span className="text-xs">
                    {order.delivery_date ? formatDate(order.delivery_date) : 'Not set'}
                  </span>
                </div>
                {order.delivery_address && (
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 mr-1 text-gray-400 mt-0.5 flex-shrink-0" />
                    <span className="text-xs text-gray-600 line-clamp-2">
                      {order.delivery_address}
                    </span>
                  </div>
                )}
              </div>
            )
          },
          {
            key: 'items_amount',
            label: 'Items & Amount',
            mobileLabel: 'Amount',
            priority: 'high',
            render: (_, order) => (
              <div>
                <div className="text-sm text-gray-900">
                  {getTotalItems(order)} items
                </div>
                <div className="text-sm font-medium text-gray-900">
                  ₹{(order.total_amount ?? 0).toLocaleString()}
                </div>
                <div className="mt-1">
                  {getPaymentStatusBadge(order.payment_status)}
                </div>
              </div>
            )
          },
          {
            key: 'status',
            label: 'Status',
            priority: 'high',
            render: (_, order) => getStatusBadge(order.status)
          },
          {
            key: 'vehicle_details',
            label: 'Vehicle Details',
            mobileLabel: 'Vehicle',
            priority: 'low',
            hideOnMobile: true,
            render: (_, order) => (
              order.vehicle_number || order.driver_name ? (
                <div className="text-sm text-gray-900">
                  {order.vehicle_number && (
                    <div className="flex items-center mb-1">
                      <Truck className="h-4 w-4 mr-1 text-gray-400" />
                      <span className="text-xs">{order.vehicle_number}</span>
                    </div>
                  )}
                  {order.driver_name && (
                    <div className="flex items-center mb-1">
                      <User className="h-4 w-4 mr-1 text-gray-400" />
                      <span className="text-xs">{order.driver_name}</span>
                    </div>
                  )}
                  {order.driver_contact && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-1 text-gray-400" />
                      <span className="text-xs">{order.driver_contact}</span>
                    </div>
                  )}
                </div>
              ) : (
                <span className="text-sm text-gray-500">Not assigned</span>
              )
            )
          },
          {
            key: 'actions',
            label: 'Actions',
            priority: 'high',
            render: (_, order) => (
              <div className="flex items-center justify-center space-x-3">
                <Link
                  to={`/sales/view/${order.id}`}
                  className="text-blue-600 hover:text-blue-900 flex items-center text-sm"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  <span className="hidden sm:inline">View</span>
                </Link>
                
                {order.status === 'dispatch_pending' && (
                  <button
                    onClick={() => {
                      setSelectedOrder(order);
                      setIsDispatchModalOpen(true);
                    }}
                    className="text-green-600 hover:text-green-900 flex items-center text-sm"
                  >
                    <Truck className="h-4 w-4 mr-1" />
                    <span className="hidden sm:inline">Dispatch</span>
                  </button>
                )}
              </div>
            )
          }
        ]}
        data={orders}
        loading={loading}
        cardLayout="detailed"
        emptyState={
          <div className="text-center py-12">
            <Truck className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No outstation orders</h3>
            <p className="mt-1 text-sm text-gray-500">
              No outstation orders found for dispatch management.
            </p>
          </div>
        }
      />

      {/* Dispatch Modal */}
      <DispatchOrderModal
        isOpen={isDispatchModalOpen}
        onClose={() => {
          setIsDispatchModalOpen(false);
          setSelectedOrder(null);
        }}
        order={selectedOrder}
        onDispatch={handleDispatchOrder}
      />
    </div>
  );
};

export default Dispatch;
