import React, { useState, useEffect } from 'react';
import { FileText, Search, Filter, Plus, Eye, Pencil, Trash2, Building, Settings } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import { purchaseRecordService } from '../../services/api/purchaseRecordService';
import PurchaseRecordClosureModal from '../../components/modals/PurchaseRecordClosureModal';

interface PurchaseRecord {
  id: string;
  record_number: string;
  supplier: string;
  record_date: string;
  pricing_model: string;
  items_subtotal: number;
  additional_costs_total: number;
  total_amount: number;
  status: string;
  items: Array<{
    product_name: string;
    sku_code: string;
    quantity: number;
    unit_type: string;
  }>;
}

const RecordPurchase: React.FC = () => {
  const navigate = useNavigate();
  const [records, setRecords] = useState<PurchaseRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [closureModal, setClosureModal] = useState<{
    isOpen: boolean;
    recordId: string;
    currentStatus: string;
    recordNumber: string;
  }>({
    isOpen: false,
    recordId: '',
    currentStatus: '',
    recordNumber: ''
  });

  useEffect(() => {
    loadPurchaseRecords();
  }, []);

  const loadPurchaseRecords = async () => {
    try {
      const data = await purchaseRecordService.getAll();
      setRecords(data || []);
    } catch (error) {
      console.error('Error loading purchase records:', error);
      toast.error('Failed to load purchase records');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRecord = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this purchase record?')) {
      try {
        await purchaseRecordService.delete(id);
        setRecords(prev => prev.filter(record => record.id !== id));
        toast.success('Purchase record deleted successfully');
      } catch (error) {
        console.error('Error deleting purchase record:', error);
        toast.error('Failed to delete purchase record');
      }
    }
  };

  const handleOpenClosureModal = (record: PurchaseRecord) => {
    setClosureModal({
      isOpen: true,
      recordId: record.id,
      currentStatus: record.status,
      recordNumber: record.record_number
    });
  };

  const handleCloseClosureModal = () => {
    setClosureModal({
      isOpen: false,
      recordId: '',
      currentStatus: '',
      recordNumber: ''
    });
  };

  const handleStatusUpdated = () => {
    loadPurchaseRecords();
  };

  const filteredRecords = records.filter(record => {
    const matchesSearch = 
      record.supplier.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.record_number.toLowerCase().includes(searchTerm.toLowerCase());
      
    const matchesStatus = selectedStatus === 'all' || record.status === selectedStatus;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'partial_closure':
        return 'bg-yellow-100 text-yellow-800';
      case 'full_closure':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading purchase records...</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="flex items-center">
          <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Purchase Records</h1>
        </div>
        <button 
          onClick={() => navigate('/record-purchase/new')}
          className="bg-green-600 text-white rounded-md px-3 py-2 sm:px-4 sm:py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center w-full sm:w-auto"
        >
          <Plus className="h-4 w-4 mr-1" />
          <span className="hidden xs:inline">New Purchase Record</span>
          <span className="xs:hidden">New Record</span>
        </button>
      </div>

      {/* Purchase Summary */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Total Purchases</p>
              <p className="text-lg sm:text-2xl font-bold text-gray-800 truncate">
                ₹{records.reduce((sum, record) => sum + (Number(record.total_amount) || 0), 0).toLocaleString()}
              </p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600 flex-shrink-0 ml-2">
              <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Total Records</p>
              <p className="text-lg sm:text-2xl font-bold text-gray-800">{records.length}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-blue-100 text-blue-600 flex-shrink-0 ml-2">
              <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Partial Closure</p>
              <p className="text-lg sm:text-2xl font-bold text-gray-800">
                {records.filter(record => record.status === 'partial_closure').length}
              </p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-yellow-100 text-yellow-600 flex-shrink-0 ml-2">
              <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm">
          <div className="flex justify-between items-start">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Full Closure</p>
              <p className="text-lg sm:text-2xl font-bold text-gray-800">
                {records.filter(record => record.status === 'full_closure').length}
              </p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600 flex-shrink-0 ml-2">
              <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 gap-3">
        <div className="relative flex-1 sm:max-w-xs">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-green-500 focus:border-green-500 text-sm"
            placeholder="Search records..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex items-center space-x-2 sm:space-x-3">
          <div className="flex items-center">
            <Filter className="h-4 w-4 text-gray-500 mr-1 sm:mr-2" />
            <span className="text-xs sm:text-sm text-gray-500">Status:</span>
          </div>
          <select
            className="border border-gray-300 rounded-md text-xs sm:text-sm py-2 px-2 sm:px-3 bg-white focus:outline-none focus:ring-green-500 focus:border-green-500 min-w-0"
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
          >
            <option value="all">All</option>
            <option value="partial_closure">Partial Closure</option>
            <option value="full_closure">Full Closure</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>
      </div>

      {/* Purchase Records Table - Desktop */}
      <div className="hidden lg:block bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Record Details
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Supplier
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Items
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Amount
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRecords.map((record) => (
                <tr key={record.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                        <FileText className="h-5 w-5" />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{record.record_number}</div>
                        <div className="text-sm text-gray-500">{formatDateTime(record.record_date)}</div>
                        <div className="text-sm text-gray-500 capitalize">{record.pricing_model}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Building className="h-4 w-4 text-gray-400 mr-2" />
                      <div className="text-sm text-gray-900">{record.supplier}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {record.items?.slice(0, 2).map((item: any, index: number) => (
                        <div key={index}>
                          {item.product_name} - {item.quantity} {item.unit_type === 'box' ? 'boxes' : 'kg'}
                        </div>
                      ))}
                      {record.items && record.items.length > 2 && (
                        <div className="text-sm text-gray-500">
                          +{record.items.length - 2} more items
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">₹{record.total_amount.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">
                      Items: ₹{record.items_subtotal.toLocaleString()}
                      {record.additional_costs_total > 0 && (
                        <span> + ₹{record.additional_costs_total.toLocaleString()}</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(record.status)}`}>
                      {record.status === 'partial_closure' ? 'Partial Closure' : 
                       record.status === 'full_closure' ? 'Full Closure' : 
                       record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => navigate(`/record-purchase/view/${record.id}`)}
                        className="text-indigo-600 hover:text-indigo-900"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      {record.status === 'partial_closure' && (
                        <button 
                          onClick={() => navigate(`/record-purchase/edit/${record.id}`)}
                          className="text-gray-600 hover:text-gray-900"
                          title="Edit Record"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                      )}
                      {record.status !== 'cancelled' && (
                        <button 
                          onClick={() => handleOpenClosureModal(record)}
                          className="text-blue-600 hover:text-blue-900"
                          title="Manage Closure Status"
                        >
                          <Settings className="h-4 w-4" />
                        </button>
                      )}
                      {record.status === 'partial_closure' && (
                        <button 
                          onClick={() => handleDeleteRecord(record.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete Record"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Purchase Records Cards - Mobile & Tablet */}
      <div className="lg:hidden space-y-4">
        {filteredRecords.map((record) => (
          <div key={record.id} className="bg-white shadow-sm rounded-lg p-4 border border-gray-200">
            {/* Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center">
                <div className="flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                  <FileText className="h-4 w-4 sm:h-5 sm:w-5" />
                </div>
                <div className="ml-3">
                  <div className="text-sm sm:text-base font-medium text-gray-900">{record.record_number}</div>
                  <div className="text-xs sm:text-sm text-gray-500">{formatDateTime(record.record_date)}</div>
                </div>
              </div>
              <span className={`px-2 py-1 inline-flex text-xs leading-4 font-semibold rounded-full ${getStatusColor(record.status)}`}>
                {record.status === 'partial_closure' ? 'Partial' : 
                 record.status === 'full_closure' ? 'Full' : 
                 record.status.charAt(0).toUpperCase() + record.status.slice(1)}
              </span>
            </div>

            {/* Content */}
            <div className="space-y-3">
              {/* Supplier */}
              <div className="flex items-center">
                <Building className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                <span className="text-sm text-gray-900 truncate">{record.supplier}</span>
              </div>

              {/* Items */}
              <div>
                <div className="text-xs text-gray-500 mb-1">Items:</div>
                <div className="text-sm text-gray-900">
                  {record.items?.slice(0, 1).map((item: any, index: number) => (
                    <div key={index} className="truncate">
                      {item.product_name} - {item.quantity} {item.unit_type === 'box' ? 'boxes' : 'kg'}
                    </div>
                  ))}
                  {record.items && record.items.length > 1 && (
                    <div className="text-xs text-gray-500">
                      +{record.items.length - 1} more items
                    </div>
                  )}
                </div>
              </div>

              {/* Amount */}
              <div className="flex justify-between items-center">
                <div>
                  <div className="text-xs text-gray-500">Total Amount</div>
                  <div className="text-sm sm:text-base font-semibold text-gray-900">₹{record.total_amount.toLocaleString()}</div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-500">Items: ₹{record.items_subtotal.toLocaleString()}</div>
                  {record.additional_costs_total > 0 && (
                    <div className="text-xs text-gray-500">+ ₹{record.additional_costs_total.toLocaleString()}</div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-2 border-t border-gray-100">
                <button 
                  onClick={() => navigate(`/record-purchase/view/${record.id}`)}
                  className="text-indigo-600 hover:text-indigo-900 p-1"
                  title="View Details"
                >
                  <Eye className="h-4 w-4" />
                </button>
                {record.status === 'partial_closure' && (
                  <button 
                    onClick={() => navigate(`/record-purchase/edit/${record.id}`)}
                    className="text-gray-600 hover:text-gray-900 p-1"
                    title="Edit Record"
                  >
                    <Pencil className="h-4 w-4" />
                  </button>
                )}
                {record.status !== 'cancelled' && (
                  <button 
                    onClick={() => handleOpenClosureModal(record)}
                    className="text-blue-600 hover:text-blue-900 p-1"
                    title="Manage Closure Status"
                  >
                    <Settings className="h-4 w-4" />
                  </button>
                )}
                {record.status === 'partial_closure' && (
                  <button 
                    onClick={() => handleDeleteRecord(record.id)}
                    className="text-red-600 hover:text-red-900 p-1"
                    title="Delete Record"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredRecords.length === 0 && !loading && (
        <div className="bg-white shadow-sm rounded-lg">
          <div className="py-12 text-center text-gray-500">
            <FileText className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Purchase Records Found</h3>
            <p className="text-sm text-gray-500 mb-4">
              {records.length === 0 
                ? "Get started by creating your first purchase record."
                : "No records match your current search and filter criteria."
              }
            </p>
            {records.length === 0 && (
              <button
                onClick={() => navigate('/record-purchase/new')}
                className="bg-green-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200"
              >
                Create First Purchase Record
              </button>
            )}
          </div>
        </div>
      )}

      {/* Closure Modal */}
      <PurchaseRecordClosureModal
        isOpen={closureModal.isOpen}
        onClose={handleCloseClosureModal}
        recordId={closureModal.recordId}
        currentStatus={closureModal.currentStatus}
        recordNumber={closureModal.recordNumber}
        onStatusUpdated={handleStatusUpdated}
      />
    </div>
  );
};

export default RecordPurchase;
