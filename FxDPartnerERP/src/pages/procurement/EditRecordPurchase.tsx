import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { FileText, ArrowLeft, Plus, Trash2, Calendar, Package }  from 'lucide-react';
import { toast } from 'react-hot-toast';
import { purchaseRecordService } from '../../services/api/purchaseRecordService';
import { supplierService } from '../../services/api/supplierService';
import EditableDropdown from '../../components/ui/EditableDropdown';

interface PurchaseRecordItem {
  id: string;
  productId: string;
  productName: string;
  skuId: string;
  skuCode: string;
  category: string;
  quantity: number;
  unitType: string;
  totalWeight: number;
  marketPrice: number;
  commission: number;
  unitPrice: number;
  total: number;
}

interface AdditionalCost {
  id: string;
  name: string;
  amount: number;
  type: 'fixed' | 'percentage' | 'per_box';
  calculatedAmount: number;
}

interface PurchaseRecordData {
  id: string;
  vehicle_arrival_id: string | null;
  supplier_id: string | null;
  record_number: string;
  supplier: string;
  record_date: string;
  arrival_timestamp: string;
  pricing_model: string;
  default_commission: number | null;
  payment_terms: number | null;
  items_subtotal: number;
  additional_costs_total: number;
  total_amount: number;
  status: string;
  notes: string | null;
  vehicle_arrival_items?: Array<{
    id: string;
    product_id: string;
    sku_id: string;
    quantity: number;
    final_quantity?: number;
    total_weight: number;
    final_total_weight?: number;
    unit_type: string;
  }>;
  items: Array<{
    id: string;
    purchase_record_id: string;
    product_id: string;
    sku_id: string;
    product_name: string;
    sku_code: string;
    category: string;
    quantity: number;
    unit_type: string;
    total_weight: number;
    market_price: number | null;
    commission: number | null;
    unit_price: number;
    total: number;
    created_at: string | null;
    updated_at: string | null;
  }>;
  costs: Array<{
    id: string;
    purchase_record_id: string;
    name: string;
    amount: number;
    type: string;
    calculated_amount: number | null;
    created_at: string | null;
    updated_at: string | null;
  }>;
}

interface Supplier {
  id: string;
  company_name: string;
  payment_terms: number;
}

const EditRecordPurchase: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [recordData, setRecordData] = useState<PurchaseRecordData | null>(null);

  const [formData, setFormData] = useState({
    supplierId: '',
    recordNumber: '',
    supplier: '',
    recordDate: '',
    arrivalTimestamp: '',
    pricingModel: 'commission',
    defaultCommission: 8,
    paymentTerms: 30,
    notes: ''
  });

  const [items, setItems] = useState<PurchaseRecordItem[]>([]);
  const [additionalCosts, setAdditionalCosts] = useState<AdditionalCost[]>([]);

  useEffect(() => {
    if (id) {
      loadRecordData();
    }
  }, [id]);

  const loadRecordData = async () => {
    if (!id) return;

    try {
      const [recordInfo, suppliersData] = await Promise.all([
        purchaseRecordService.getById(id),
        supplierService.getAll()
      ]);

      // Transform the API response to match the expected type
      interface CostData {
        id: string;
        purchase_record_id: string;
        name: string;
        amount: number;
        type: string;
        calculated_amount: number | null;
        created_at: string | null;
        updated_at: string | null;
      }
      
      const transformedRecordInfo: PurchaseRecordData = {
        ...recordInfo,
        costs: recordInfo.costs?.map((cost: CostData) => ({
          ...cost,
          calculated_amount: cost.calculated_amount || null,
          updated_at: cost.updated_at || null
        })) || []
      };

      // Check if record is fully closed
      if (transformedRecordInfo.status === 'full_closure') {
        toast.error('This purchase record is fully closed and cannot be edited');
        navigate('/record-purchase');
        return;
      }

      setRecordData(transformedRecordInfo);
      setSuppliers(suppliersData || []);

      // Set form data with null checks
      setFormData({
        supplierId: transformedRecordInfo.supplier_id || '',
        recordNumber: transformedRecordInfo.record_number,
        supplier: transformedRecordInfo.supplier,
        recordDate: new Date(transformedRecordInfo.record_date).toISOString().slice(0, 16),
        arrivalTimestamp: new Date(transformedRecordInfo.arrival_timestamp).toISOString().slice(0, 16),
        pricingModel: transformedRecordInfo.pricing_model,
        defaultCommission: transformedRecordInfo.default_commission || 8,
        paymentTerms: transformedRecordInfo.payment_terms || 30,
        notes: transformedRecordInfo.notes || ''
      });

      // Set items, using final_quantity if available from vehicle arrival
      const recordItems = transformedRecordInfo.items.map((item) => {
        // If this is from a vehicle arrival, try to get the final quantities
        let quantity = item.quantity;
        let totalWeight = item.total_weight;

        if (transformedRecordInfo.vehicle_arrival_id) {
          // Find the corresponding vehicle arrival item to get final quantities
          const vehicleArrivalItem = transformedRecordInfo.vehicle_arrival_items?.find(
            (vai) => vai.product_id === item.product_id && vai.sku_id === item.sku_id
          );
          if (vehicleArrivalItem) {
            quantity = vehicleArrivalItem.final_quantity || quantity;
            totalWeight = vehicleArrivalItem.final_total_weight || totalWeight;
          }
        }

        return {
        id: item.id,
        productId: item.product_id,
        productName: item.product_name,
        skuId: item.sku_id,
        skuCode: item.sku_code,
        category: item.category,
          quantity,
        unitType: item.unit_type,
          totalWeight,
        marketPrice: Number(item.market_price) || 0,
        commission: Number(item.commission) || 0,
        unitPrice: Number(item.unit_price) || 0,
        total: Number(item.total) || 0
        };
      });
      setItems(recordItems);

      // Set additional costs
      const recordCosts = transformedRecordInfo.costs.map((cost) => ({
        id: cost.id,
        name: cost.name,
        amount: Number(cost.amount) || 0,
        type: cost.type as 'fixed' | 'percentage' | 'per_box',
        calculatedAmount: Number(cost.calculated_amount) || 0
      }));
      setAdditionalCosts(recordCosts);

    } catch (error) {
      console.error('Error loading purchase record:', error);
      toast.error('Failed to load purchase record data');
      navigate('/record-purchase');
    } finally {
      setLoading(false);
    }
  };

  const handleSupplierChange = (value: string) => {
    // Check if the value is a supplier ID or a supplier name
    let supplier = suppliers.find(s => s.id === value);
    
    // If not found by ID, try to find by name (for custom values or when set from vehicle arrival)
    if (!supplier) {
      supplier = suppliers.find(s => s.company_name === value);
    }
    
    if (supplier) {
      setFormData(prev => ({
        ...prev,
        supplierId: supplier!.id,
        supplier: supplier!.company_name,
        paymentTerms: supplier!.payment_terms
      }));
    } else {
      // If it's a custom supplier name (not in the list)
      setFormData(prev => ({
        ...prev,
        supplierId: '',
        supplier: value,
        paymentTerms: formData.paymentTerms
      }));
    }
  };

  const handleDefaultCommissionChange = (value: number) => {
    // Update the form data
    setFormData(prev => ({ ...prev, defaultCommission: value }));
    
    // Update commission for all items
    setItems(prev => prev.map(item => {
      const updatedItem = { ...item, commission: value };
      // Recalculate unit price based on new commission
      if (formData.pricingModel === 'commission') {
        updatedItem.unitPrice = updatedItem.marketPrice * (1 - value / 100);
        updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
      }
      return updatedItem;
    }));
  };

  const handleItemChange = (id: string, field: keyof PurchaseRecordItem, value: string | number) => {
    setItems(prev => prev.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };
        
        // Recalculate unit price and total based on pricing model
        if (formData.pricingModel === 'commission') {
        if (field === 'marketPrice' || field === 'commission') {
            updatedItem.unitPrice = updatedItem.marketPrice * (1 - updatedItem.commission / 100);
          }
        }
        // For fixed price model, unit price is directly entered
        updatedItem.total = updatedItem.quantity * updatedItem.unitPrice;
        
        return updatedItem;
      }
      return item;
    }));
  };

  const handleAddCost = () => {
    const newCost: AdditionalCost = {
      id: `cost_${Date.now()}`,
      name: '',
      amount: 0,
      type: 'fixed',
      calculatedAmount: 0
    };
    setAdditionalCosts(prev => [...prev, newCost]);
  };

  const handleRemoveCost = (id: string) => {
    setAdditionalCosts(prev => prev.filter(cost => cost.id !== id));
  };

  const handleCostChange = (id: string, field: keyof AdditionalCost, value: string | number) => {
    setAdditionalCosts(prev => prev.map(cost => {
      if (cost.id === id) {
        const updatedCost = { ...cost, [field]: value };
        
        // Calculate the actual cost amount
        const itemsSubtotal = calculateItemsSubtotal();
        const totalBoxes = items.reduce((sum, item) => sum + (item.unitType === 'box' ? item.quantity : 0), 0);
        
        switch (updatedCost.type) {
          case 'fixed':
            updatedCost.calculatedAmount = Number(updatedCost.amount) || 0;
            break;
          case 'percentage':
            updatedCost.calculatedAmount = (itemsSubtotal * (Number(updatedCost.amount) || 0)) / 100;
            break;
          case 'per_box':
            updatedCost.calculatedAmount = (Number(updatedCost.amount) || 0) * totalBoxes;
            break;
        }
        
        return updatedCost;
      }
      return cost;
    }));
  };

  const calculateItemsSubtotal = () => {
    return items.reduce((sum, item) => {
      const itemTotal = Number(item.total) || 0;
      return sum + itemTotal;
    }, 0);
  };

  const calculateAdditionalCostsTotal = () => {
    return additionalCosts.reduce((sum, cost) => {
      const costAmount = Number(cost.calculatedAmount) || 0;
      return sum + costAmount;
    }, 0);
  };

  const calculateTotal = () => {
    const itemsSubtotal = calculateItemsSubtotal();
    const costsTotal = calculateAdditionalCostsTotal();
    return itemsSubtotal - costsTotal;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check if record is fully closed
    if (recordData?.status === 'full_closure') {
      toast.error('This purchase record is fully closed and cannot be edited');
      return;
    }
    
    if (!formData.supplierId) {
      toast.error('Please select a supplier');
      return;
    }

    if (items.length === 0) {
      toast.error('Please add at least one item');
      return;
    }

    // Validate items
    for (const item of items) {
      if (item.quantity <= 0 || item.unitPrice <= 0) {
        toast.error('Please complete all item details with valid values');
        return;
      }
    }

    setIsSubmitting(true);

    try {
      const itemsSubtotal = calculateItemsSubtotal();
      const additionalCostsTotal = calculateAdditionalCostsTotal();
      const totalAmount = calculateTotal();

      // Update purchase record
      const recordUpdateData = {
        supplier_id: formData.supplierId,
        record_number: formData.recordNumber,
        supplier: formData.supplier,
        record_date: formData.recordDate,
        arrival_timestamp: formData.arrivalTimestamp,
        pricing_model: formData.pricingModel,
        default_commission: formData.defaultCommission,
        payment_terms: formData.paymentTerms,
        items_subtotal: itemsSubtotal,
        additional_costs_total: additionalCostsTotal,
        total_amount: totalAmount,
        notes: formData.notes || null
      };

      // Prepare items data
      const itemsData = items.map(item => ({
        id: item.id,
        purchase_record_id: id!,
        product_id: item.productId,
        sku_id: item.skuId,
        product_name: item.productName,
        sku_code: item.skuCode,
        category: item.category,
        quantity: item.quantity,
        unit_type: item.unitType,
        total_weight: item.totalWeight,
        market_price: item.marketPrice,
        commission: item.commission,
        unit_price: item.unitPrice,
        total: item.total
      }));

      // Prepare costs data
      const costsData = additionalCosts.map(cost => ({
        id: cost.id,
        purchase_record_id: id!,
        name: cost.name,
        amount: cost.amount,
        type: cost.type,
        calculated_amount: cost.calculatedAmount
      }));

      await purchaseRecordService.update(id!, recordUpdateData, itemsData, costsData);
      
      toast.success('Purchase record updated successfully!');
      navigate('/record-purchase');
    } catch (error) {
      console.error('Error updating purchase record:', error);
      toast.error('Failed to update purchase record. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading purchase record...</div>
      </div>
    );
  }

  if (!recordData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Purchase record not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
      <div className="flex items-center space-x-3 sm:space-x-4">
        <button
          onClick={() => navigate('/record-purchase')}
          className="text-gray-600 hover:text-gray-900 transition-colors duration-200 p-1"
        >
          <ArrowLeft className="h-5 w-5 sm:h-6 sm:w-6" />
        </button>
        <div className="flex items-center">
          <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
          <h1 className="text-lg sm:text-2xl font-bold text-gray-800">Edit Purchase Record</h1>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg">
        {recordData?.status === 'full_closure' && (
          <div className="bg-red-50 border-l-4 border-red-400 p-3 sm:p-4 mb-4 sm:mb-6 mx-4 sm:mx-6 mt-4 sm:mt-6 rounded-r-md">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-red-700">
                  This purchase record is fully closed and cannot be edited.
                </p>
              </div>
            </div>
          </div>
        )}
        <form onSubmit={handleSubmit} className="p-4 sm:p-6 space-y-4 sm:space-y-6">
          {/* Basic Information */}
          <div>
            <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Basic Information</h2>
            <div className="grid grid-cols-1 gap-4 sm:gap-6 sm:grid-cols-2">
              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Supplier <span className="text-red-500">*</span>
                </label>
                <EditableDropdown
                  options={[
                    { value: '', label: 'Select a supplier' },
                    ...suppliers.map(supplier => ({
                      value: supplier.company_name,
                      label: supplier.company_name
                    }))
                  ]}
                  value={formData.supplier}
                  onChange={(value) => handleSupplierChange(value)}
                  placeholder="Select or search supplier..."
                  className="mt-1"
                  allowCustomValue={true}
                />
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Record Number <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.recordNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, recordNumber: e.target.value }))}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  required
                />
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Record Date <span className="text-red-500">*</span>
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  </div>
                  <input
                    type="datetime-local"
                    value={formData.recordDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, recordDate: e.target.value }))}
                    className="block w-full pl-8 sm:pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Arrival Timestamp
                </label>
                <div className="mt-1 relative rounded-md shadow-sm">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  </div>
                  <input
                    type="datetime-local"
                    value={formData.arrivalTimestamp}
                    onChange={(e) => setFormData(prev => ({ ...prev, arrivalTimestamp: e.target.value }))}
                    className="block w-full pl-8 sm:pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Pricing Model
                </label>
                <EditableDropdown
                  options={[
                    { value: 'commission', label: 'Commission Based' },
                    { value: 'fixed', label: 'Fixed Price' }
                  ]}
                  value={formData.pricingModel}
                  onChange={(value) => setFormData(prev => ({ ...prev, pricingModel: value }))}
                  placeholder="Select pricing model..."
                  className="mt-1"
                  allowCustomValue={true}
                />
              </div>

              {formData.pricingModel === 'commission' && (
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700">
                    Default Commission (%)
                  </label>
                  <input
                    type="number"
                    value={formData.defaultCommission}
                    onChange={(e) => handleDefaultCommissionChange(Number(e.target.value))}
                    min="0"
                    max="100"
                    step="0.1"
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              )}

              <div>
                <label className="block text-xs sm:text-sm font-medium text-gray-700">
                  Payment Terms (Days)
                </label>
                <input
                  type="number"
                  value={formData.paymentTerms}
                  onChange={(e) => setFormData(prev => ({ ...prev, paymentTerms: Number(e.target.value) }))}
                  min="0"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                />
              </div>
            </div>
          </div>

          {/* Items Section */}
          <div className="border-t pt-4 sm:pt-6">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Purchase Items</h2>
            </div>

            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    {formData.pricingModel === 'commission' ? (
                      <>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Market Price (₹)
                    </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Commission (%)
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price (₹)
                        </th>
                      </>
                    ) : (
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price (₹)
                      </th>
                    )}
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Total (₹)
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <Package className="h-4 w-4 text-gray-400 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                            <div className="text-sm text-gray-500">{item.skuCode} • {item.category}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {item.quantity} {item.unitType === 'box' ? 'boxes' : 'kg'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {item.totalWeight} kg total
                        </div>
                      </td>
                      {formData.pricingModel === 'commission' ? (
                        <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          value={item.marketPrice}
                          onChange={(e) => handleItemChange(item.id, 'marketPrice', Number(e.target.value))}
                          min="0"
                          step="0.01"
                          className="block w-24 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                        />
                      </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="number"
                            value={item.commission}
                            onChange={(e) => handleItemChange(item.id, 'commission', Number(e.target.value))}
                            min="0"
                            max="100"
                            step="0.1"
                            className="block w-20 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="number"
                              value={item.unitPrice}
                              onChange={(e) => handleItemChange(item.id, 'unitPrice', Number(e.target.value))}
                              min="0"
                              step="0.01"
                              className="block w-24 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              disabled
                            />
                          </td>
                        </>
                      ) : (
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          value={item.unitPrice}
                          onChange={(e) => handleItemChange(item.id, 'unitPrice', Number(e.target.value))}
                          min="0"
                          step="0.01"
                          className="block w-24 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            placeholder="Enter unit price"
                        />
                      </td>
                      )}
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ₹{item.total.toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile/Tablet Cards */}
            <div className="lg:hidden space-y-4">
              {items.map((item) => (
                <div key={item.id} className="bg-gray-50 rounded-lg p-4 border">
                  <div className="space-y-3">
                    {/* Product Info */}
                    <div className="flex items-start">
                      <Package className="h-4 w-4 text-gray-400 mr-2 mt-1 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <h3 className="text-sm font-medium text-gray-900 truncate">{item.productName}</h3>
                        <div className="text-xs text-gray-500 mt-1">
                          <div>{item.skuCode} • {item.category}</div>
                        </div>
                      </div>
                    </div>

                    {/* Quantity Info */}
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="text-xs text-gray-500">Quantity</div>
                        <div className="text-sm font-medium text-gray-900">
                          {item.quantity} {item.unitType === 'box' ? 'boxes' : 'kg'}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-gray-500">Total Weight</div>
                        <div className="text-sm font-medium text-gray-900">{item.totalWeight} kg</div>
                      </div>
                    </div>

                    {/* Pricing Inputs */}
                    {formData.pricingModel === 'commission' ? (
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Market Price (₹)</label>
                          <input
                            type="number"
                            value={item.marketPrice}
                            onChange={(e) => handleItemChange(item.id, 'marketPrice', Number(e.target.value))}
                            min="0"
                            step="0.01"
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Commission (%)</label>
                          <input
                            type="number"
                            value={item.commission}
                            onChange={(e) => handleItemChange(item.id, 'commission', Number(e.target.value))}
                            min="0"
                            max="100"
                            step="0.1"
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Unit Price (₹)</label>
                          <input
                            type="number"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(item.id, 'unitPrice', Number(e.target.value))}
                            min="0"
                            step="0.01"
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500 bg-gray-100"
                            disabled
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Total (₹)</label>
                          <div className="text-sm font-bold text-gray-900 py-1">₹{item.total.toFixed(2)}</div>
                        </div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Unit Price (₹)</label>
                          <input
                            type="number"
                            value={item.unitPrice}
                            onChange={(e) => handleItemChange(item.id, 'unitPrice', Number(e.target.value))}
                            min="0"
                            step="0.01"
                            className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                            placeholder="Enter unit price"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Total (₹)</label>
                          <div className="text-sm font-bold text-gray-900 py-1">₹{item.total.toFixed(2)}</div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Costs */}
          <div className="border-t pt-4 sm:pt-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 space-y-2 sm:space-y-0">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Additional Costs</h2>
              <button
                type="button"
                onClick={handleAddCost}
                className="bg-green-600 text-white rounded-md px-3 py-2 sm:px-4 sm:py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center"
              >
                <Plus className="h-4 w-4 mr-1" />
                <span className="hidden xs:inline">Add Cost</span>
                <span className="xs:hidden">Add</span>
              </button>
            </div>

            {additionalCosts.length > 0 && (
              <>
                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-visible">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Cost Name
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Calculated
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {additionalCosts.map((cost) => (
                        <tr key={cost.id}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <input
                              type="text"
                              value={cost.name}
                              onChange={(e) => handleCostChange(cost.id, 'name', e.target.value)}
                              className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              placeholder="Cost name"
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <EditableDropdown
                              options={[
                                { value: 'fixed', label: 'Fixed Amount' },
                                { value: 'percentage', label: 'Percentage' },
                                { value: 'per_box', label: 'Per Box' }
                              ]}
                              value={cost.type}
                              onChange={(value) => handleCostChange(cost.id, 'type', value as 'fixed' | 'percentage' | 'per_box')}
                              placeholder="Select type..."
                              className="w-full"
                              allowCustomValue={true}
                            />
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="relative">
                              <input
                                type="number"
                                value={cost.amount}
                                onChange={(e) => handleCostChange(cost.id, 'amount', Number(e.target.value))}
                                min="0"
                                step="0.01"
                                className="block w-24 border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              />
                              {cost.type === 'percentage' && (
                                <span className="absolute right-2 top-1 text-xs text-gray-500">%</span>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ₹{(typeof cost.calculatedAmount === 'number' ? cost.calculatedAmount.toFixed(2) : '0.00')}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <button
                              type="button"
                              onClick={() => handleRemoveCost(cost.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Mobile/Tablet Cards */}
                <div className="lg:hidden space-y-4">
                  {additionalCosts.map((cost) => (
                    <div key={cost.id} className="bg-gray-50 rounded-lg p-4 border">
                      <div className="space-y-3">
                        <div className="flex justify-between items-start">
                          <div className="flex-1 mr-3">
                            <label className="block text-xs text-gray-500 mb-1">Cost Name</label>
                            <input
                              type="text"
                              value={cost.name}
                              onChange={(e) => handleCostChange(cost.id, 'name', e.target.value)}
                              className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              placeholder="Cost name"
                            />
                          </div>
                          <button
                            type="button"
                            onClick={() => handleRemoveCost(cost.id)}
                            className="text-red-600 hover:text-red-900 p-1"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>

                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="block text-xs text-gray-500 mb-1">Type</label>
                            <EditableDropdown
                              options={[
                                { value: 'fixed', label: 'Fixed Amount' },
                                { value: 'percentage', label: 'Percentage' },
                                { value: 'per_box', label: 'Per Box' }
                              ]}
                              value={cost.type}
                              onChange={(value) => handleCostChange(cost.id, 'type', value as 'fixed' | 'percentage' | 'per_box')}
                              placeholder="Select type..."
                              className="w-full"
                              allowCustomValue={true}
                            />
                          </div>
                          <div>
                            <label className="block text-xs text-gray-500 mb-1">Amount</label>
                            <div className="relative">
                              <input
                                type="number"
                                value={cost.amount}
                                onChange={(e) => handleCostChange(cost.id, 'amount', Number(e.target.value))}
                                min="0"
                                step="0.01"
                                className="block w-full border border-gray-300 rounded-md shadow-sm py-1 px-2 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                              />
                              {cost.type === 'percentage' && (
                                <span className="absolute right-2 top-1 text-xs text-gray-500">%</span>
                              )}
                            </div>
                          </div>
                        </div>

                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Calculated Amount</label>
                          <div className="text-sm font-bold text-gray-900">₹{(typeof cost.calculatedAmount === 'number' ? cost.calculatedAmount.toFixed(2) : '0.00')}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>

          {/* Summary */}
          <div className="border-t pt-4 sm:pt-6">
            <h2 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">Purchase Summary</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-xs sm:text-sm font-medium text-gray-700">
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Any additional notes..."
                  />
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-3 sm:p-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Items Subtotal:</span>
                    <span className="text-gray-900">₹{calculateItemsSubtotal().toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Additional Costs:</span>
                    <span className="text-gray-900">₹{calculateAdditionalCostsTotal().toFixed(2)}</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between text-base sm:text-lg font-bold">
                    <span className="text-gray-900">Total Amount:</span>
                    <span className="text-green-600">₹{calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="border-t pt-4 sm:pt-6 flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
            <button
              type="button"
              onClick={() => navigate('/record-purchase')}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 order-2 sm:order-1"
              disabled={isSubmitting}
            >
              {recordData?.status === 'full_closure' ? 'Back' : 'Cancel'}
            </button>
            {recordData?.status !== 'full_closure' && (
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:bg-gray-400 disabled:cursor-not-allowed order-1 sm:order-2"
              >
                {isSubmitting ? 'Updating Record...' : 'Update Purchase Record'}
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditRecordPurchase;
