import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { Package, Search, Filter, RefreshCw, Truck, Calendar, AlertCircle, ShoppingCart, X, Plus, Minus, Settings, ChevronDown, Trash2, Edit3, BarChart3 } from 'lucide-react';
import { toast } from 'react-hot-toast';
import { vehicleArrivalService } from '../../services/api/vehicleArrivalService';
import { salesService } from '../../services/api/salesService';
import { inventoryService } from '../../services/api/inventoryService';
import MobileTable from '../../components/ui/MobileTable';
import MarkDumpModal from '../../components/modals/MarkDumpModal';
import ManualAdjustmentModal from '../../components/modals/ManualAdjustmentModal';
import UpdateSKUModal from '../../components/modals/UpdateSKUModal';

interface TimelineEntry {
  id: string;
  date: string;
  type: 'arrival' | 'sale';
  quantity: number;
  weight?: number;
  source: string; // supplier or customer name
  details: string; // vehicle number or order number
  status?: string;
}

interface InventoryItem {
  id: string;
  productId: string;
  productName: string;
  skuId: string;
  skuCode: string;
  unitType: 'box' | 'loose';
  currentQuantity: number;  // From current_inventory table
  currentWeight: number;    // From current_inventory table
  totalQuantity: number;    // From vehicle arrivals (historical)
  totalWeight: number;      // From vehicle arrivals (historical)
  lastArrival: string;
  arrivalCount: number;
  supplier: string;
  vehicleArrivals: Array<{
    id: string;
    arrivalTime: string;
    supplier: string;
    vehicleNumber: string | null;
    quantity: number;
    weight: number;
    status: string;
    purchaseRecordNumber?: string | null;
    purchaseRecordId?: string | null;
  }>;
  salesOrders: Array<{
    id: string;
    orderNumber: string;
    orderDate: string;
    customerName: string;
    quantity: number;
    status: string;
  }>;
}

const Inventory: React.FC = () => {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedUnitType, setSelectedUnitType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  
  // Inventory adjustment modals
  const [showAdjustmentDropdown, setShowAdjustmentDropdown] = useState<string | null>(null);
  const [dropdownPosition, setDropdownPosition] = useState<{ top: number; left: number } | null>(null);
  const [showMarkDumpModal, setShowMarkDumpModal] = useState(false);
  const [showManualAdjustmentModal, setShowManualAdjustmentModal] = useState(false);
  const [showUpdateSKUModal, setShowUpdateSKUModal] = useState(false);
  const [selectedAdjustmentItem, setSelectedAdjustmentItem] = useState<InventoryItem | null>(null);
  const dropdownButtonRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});

  useEffect(() => {
    loadInventoryData();
  }, []);

  // Close dropdown when clicking outside or scrolling
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showAdjustmentDropdown) {
        const target = event.target as HTMLElement;
        // Check if click is outside both the button container and the portal dropdown
        if (!target.closest('.adjustment-dropdown-container') && !target.closest('.portal-dropdown')) {
          setShowAdjustmentDropdown(null);
          setDropdownPosition(null);
        }
      }
    };

    const handleScroll = () => {
      if (showAdjustmentDropdown) {
        setShowAdjustmentDropdown(null);
        setDropdownPosition(null);
      }
    };

    const handleResize = () => {
      if (showAdjustmentDropdown) {
        setShowAdjustmentDropdown(null);
        setDropdownPosition(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    window.addEventListener('scroll', handleScroll, true); // Use capture to catch all scroll events
    window.addEventListener('resize', handleResize);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [showAdjustmentDropdown]);

  const loadInventoryData = async () => {
    try {
      setError(null);
      setLoading(true);
      
      // Get current inventory data using the API function
      const currentInventory = await inventoryService.getAvailable();
      
      // Debug: Log the current inventory data
      console.log('Current Inventory Data:', currentInventory);
      
      // Get all vehicle arrivals and filter for completed and record created ones
      const arrivals = (await vehicleArrivalService.getAll()).filter(
        (arrival: any) => arrival.status === 'completed' || arrival.status === 'po_created'
      );

      // Get all sales orders
      const salesOrders = await salesService.getAll();
      
      // Create inventory items from current inventory data
      const inventoryArray = currentInventory.map((item: any) => {
        // Debug: Log each inventory item being processed
        console.log(`Processing SKU ${item.sku_code}:`, {
          productName: item.product_name,
          available_quantity: item.available_quantity,
          total_weight: item.total_weight
        });
        
        const inventoryItem: InventoryItem = {
          id: `${item.product_id}_${item.sku_id}`,
          productId: item.product_id,
          productName: item.product_name,
          skuId: item.sku_id,
          skuCode: item.sku_code,
          unitType: item.unit_type as 'box' | 'loose',
          currentQuantity: item.available_quantity,
          currentWeight: item.total_weight,
          totalQuantity: 0,
          totalWeight: 0,
          lastArrival: item.last_updated_at,
          arrivalCount: 0,
          supplier: 'N/A',
          vehicleArrivals: [],
          salesOrders: []
        };

        // Add historical data from vehicle arrivals
        arrivals.forEach((arrival: any) => {
          if (!arrival.items) return;

          arrival.items.forEach((arrivalItem: any) => {
            if (arrivalItem.product_id === item.product_id && arrivalItem.sku_id === item.sku_id) {
              inventoryItem.totalQuantity += arrivalItem.final_quantity || arrivalItem.quantity;
              inventoryItem.totalWeight += arrivalItem.final_total_weight || arrivalItem.total_weight;
              inventoryItem.arrivalCount += 1;
              
              // Get the purchase record number if available
              const purchaseRecord = arrival.purchase_records && arrival.purchase_records.length > 0 
                ? arrival.purchase_records[0] 
                : null;
              
              inventoryItem.vehicleArrivals.push({
                id: arrival.id,
                arrivalTime: arrival.arrival_time,
                supplier: arrival.supplier,
                vehicleNumber: arrival.vehicle_number,
                quantity: arrivalItem.final_quantity || arrivalItem.quantity,
                weight: arrivalItem.final_total_weight || arrivalItem.total_weight,
                status: arrival.status,
                purchaseRecordNumber: purchaseRecord?.record_number || null,
                purchaseRecordId: purchaseRecord?.id || null
              });
            }
          });
        });

        // Add sales order data
        salesOrders.forEach((order: any) => {
          if (!order.sales_order_items) return;

          order.sales_order_items.forEach((orderItem: any) => {
            if (orderItem.product_id === item.product_id && orderItem.sku_id === item.sku_id) {
              inventoryItem.salesOrders.push({
                id: order.id,
                orderNumber: order.order_number,
                orderDate: order.order_date,
                customerName: order.customer.name,
                quantity: orderItem.quantity,
                status: order.status
              });
            }
          });
        });

        // Sort vehicle arrivals and sales orders by date (most recent first)
        inventoryItem.vehicleArrivals.sort((a, b) => 
          new Date(b.arrivalTime).getTime() - new Date(a.arrivalTime).getTime()
        );
        inventoryItem.salesOrders.sort((a, b) => 
          new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime()
        );

        // Update lastArrival if there are vehicle arrivals
        if (inventoryItem.vehicleArrivals.length > 0) {
          inventoryItem.lastArrival = inventoryItem.vehicleArrivals[0].arrivalTime;
          inventoryItem.supplier = inventoryItem.vehicleArrivals[0].supplier;
        }

        return inventoryItem;
      });
      
      // Debug: Log the final inventory array
      console.log('Final Inventory Array:', inventoryArray);
      
      // Sort inventory items by product name
      inventoryArray.sort((a: InventoryItem, b: InventoryItem) => a.productName.localeCompare(b.productName));
      
      setInventoryItems(inventoryArray);
    } catch (error) {
      console.error('Error loading inventory data:', error);
      setError('Failed to load inventory data');
    } finally {
      setLoading(false);
    }
  };

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = 
      item.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.skuCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.supplier.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getTotalInventoryValue = () => {
    const negativeSkus = inventoryItems.filter(item => (Number(item.currentQuantity) || 0) < 0);
    const totalCurrentStock = inventoryItems.reduce((sum, item) => sum + (Number(item.currentQuantity) || 0), 0);
    const negativeStock = inventoryItems.reduce((sum, item) => {
      const qty = Number(item.currentQuantity) || 0;
      return qty < 0 ? sum + Math.abs(qty) : sum;
    }, 0);
    
    return {
      totalItems: inventoryItems.length,
      negativeSkus: negativeSkus.length,
      currentStock: totalCurrentStock,
      negativeStock: negativeStock,
      currentQuantity: inventoryItems.reduce((sum, item) => sum + (Number(item.currentQuantity) || 0), 0),
      currentWeight: inventoryItems.reduce((sum, item) => sum + (Number(item.currentWeight) || 0), 0),
      totalArrivals: inventoryItems.reduce((sum, item) => sum + (Number(item.arrivalCount) || 0), 0)
    };
  };


  const inventoryStats = getTotalInventoryValue();

  const createTimelineEntries = (item: InventoryItem): TimelineEntry[] => {
    const entries: TimelineEntry[] = [];

    // Add arrival entries
    item.vehicleArrivals.forEach((arrival, index) => {
      entries.push({
        id: `arrival-${arrival.id}-${item.skuId}-${index}`,
        date: arrival.arrivalTime,
        type: 'arrival',
        quantity: arrival.quantity,
        weight: arrival.weight,
        source: arrival.supplier,
        details: arrival.purchaseRecordNumber || `VA-${arrival.id.slice(-8)}`, // Use PR number or fallback to vehicle arrival ID
        status: arrival.status
      });
    });

    // Add sales entries
    item.salesOrders.forEach((order, index) => {
      entries.push({
        id: `sale-${order.id}-${item.skuId}-${index}`,
        date: order.orderDate,
        type: 'sale',
        quantity: order.quantity,
        source: order.customerName,
        details: order.orderNumber,
        status: order.status
      });
    });

    // Sort by date (most recent first)
    return entries.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  const handleViewHistory = (item: InventoryItem) => {
    setSelectedItem(item);
    setShowHistoryModal(true);
  };

  const handleAdjustClick = (item: InventoryItem, event: React.MouseEvent<HTMLButtonElement>) => {
    const button = event.currentTarget;
    const rect = button.getBoundingClientRect();
    
    setSelectedAdjustmentItem(item);
    
    if (showAdjustmentDropdown === item.id) {
      setShowAdjustmentDropdown(null);
      setDropdownPosition(null);
    } else {
      // Calculate position for dropdown
      const dropdownWidth = 192; // w-48 = 12rem = 192px
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      
      let left = rect.left;
      let top = rect.bottom + 4; // mt-1 = 4px
      
      // Adjust if dropdown would go off-screen horizontally
      if (left + dropdownWidth > viewportWidth) {
        left = rect.right - dropdownWidth;
      }
      
      // Adjust if dropdown would go off-screen vertically
      const dropdownHeight = 120; // Approximate height for 3 items
      if (top + dropdownHeight > viewportHeight) {
        top = rect.top - dropdownHeight - 4;
      }
      
      setDropdownPosition({ top, left });
      setShowAdjustmentDropdown(item.id);
    }
  };

  // Portal dropdown component
  const PortalDropdown = () => {
    if (!showAdjustmentDropdown || !dropdownPosition) return null;

    return createPortal(
      <div 
        className="portal-dropdown fixed w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50 max-h-60 overflow-y-auto"
        style={{
          top: dropdownPosition.top,
          left: dropdownPosition.left,
        }}
      >
        <div className="py-1">
          <button
            onClick={() => {
              setShowAdjustmentDropdown(null);
              setDropdownPosition(null);
              setShowMarkDumpModal(true);
            }}
            className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <Trash2 className="h-3 w-3 mr-2 text-red-500" />
            Mark as Dump
          </button>
          <button
            onClick={() => {
              setShowAdjustmentDropdown(null);
              setDropdownPosition(null);
              setShowUpdateSKUModal(true);
            }}
            className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <Edit3 className="h-3 w-3 mr-2 text-purple-500" />
            Update Product & SKU
          </button>
          <button
            onClick={() => {
              setShowAdjustmentDropdown(null);
              setDropdownPosition(null);
              setShowManualAdjustmentModal(true);
            }}
            className="flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100"
          >
            <Settings className="h-3 w-3 mr-2 text-blue-500" />
            Manual Adjustment
          </button>
        </div>
      </div>,
      document.body
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading inventory...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Package className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-800">Inventory Management</h1>
          </div>
          <button 
            onClick={loadInventoryData}
            className="bg-green-600 text-white rounded-md px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Retry
          </button>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex items-center">
            <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-sm font-medium text-red-800">{error}</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
        <div className="flex items-center">
          <Package className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
          <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-800">Inventory Management</h1>
        </div>
        <button 
          onClick={loadInventoryData}
          className="bg-green-600 text-white rounded-md px-3 sm:px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center justify-center w-full sm:w-auto"
        >
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh Inventory
        </button>
      </div>
      
      {/* Inventory Summary */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <div className="bg-gradient-to-br from-green-50 to-green-100 p-3 sm:p-4 rounded-xl shadow-sm border border-green-200 hover:shadow-md transition-shadow duration-200">
          <div className="flex justify-between items-start">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-semibold text-green-700 truncate">Total SKUs</p>
              <p className="text-lg sm:text-2xl font-bold text-green-800">{inventoryStats.totalItems}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-green-200 text-green-700 flex-shrink-0 ml-2 shadow-sm">
              <Package className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-red-50 to-red-100 p-3 sm:p-4 rounded-xl shadow-sm border border-red-200 hover:shadow-md transition-shadow duration-200">
          <div className="flex justify-between items-start">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-semibold text-red-700 truncate">Negative SKUs</p>
              <p className="text-lg sm:text-2xl font-bold text-red-800">{inventoryStats.negativeSkus}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-red-200 text-red-700 flex-shrink-0 ml-2 shadow-sm">
              <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-3 sm:p-4 rounded-xl shadow-sm border border-blue-200 hover:shadow-md transition-shadow duration-200">
          <div className="flex justify-between items-start">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-semibold text-blue-700 truncate">Current Stock</p>
              <p className="text-lg sm:text-2xl font-bold text-blue-800">{inventoryStats.currentStock}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-blue-200 text-blue-700 flex-shrink-0 ml-2 shadow-sm">
              <Package className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
        <div className="bg-gradient-to-br from-orange-50 to-orange-100 p-3 sm:p-4 rounded-xl shadow-sm border border-orange-200 hover:shadow-md transition-shadow duration-200">
          <div className="flex justify-between items-start">
            <div className="min-w-0 flex-1">
              <p className="text-xs sm:text-sm font-semibold text-orange-700 truncate">Negative Stock</p>
              <p className="text-lg sm:text-2xl font-bold text-orange-800">{inventoryStats.negativeStock}</p>
            </div>
            <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-orange-200 text-orange-700 flex-shrink-0 ml-2 shadow-sm">
              <Minus className="h-4 w-4 sm:h-5 sm:w-5" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm transition-all duration-200"
            placeholder="Search inventory..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>
      
      {/* Inventory Table */}
      <MobileTable
        columns={[
          {
            key: 'productDetails',
            label: 'Product Details',
            mobileLabel: 'Product',
            render: (_, item) => (
              <div className="flex items-center">
                <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600">
                  <Package className="h-5 w-5" />
                </div>
                <div className="ml-4">
                  <div className="text-sm font-medium text-gray-900">{item.productName}</div>
                  <div className="text-sm text-gray-500">Product</div>
                </div>
              </div>
            )
          },
          {
            key: 'skuDetails',
            label: 'SKU & Packaging',
            mobileLabel: 'SKU',
            render: (_, item) => (
              <div>
                <div className="text-sm text-gray-900">{item.skuCode}</div>
                <span className={`inline-flex px-2 text-xs leading-5 font-semibold rounded-full ${
                  item.unitType === 'box' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                }`}>
                  {item.unitType === 'box' ? 'Box/Crate' : 'Loose'}
                </span>
              </div>
            )
          },
          {
            key: 'currentStock',
            label: 'Current Stock',
            mobileLabel: 'Stock',
            render: (_, item) => (
              <div className="text-sm text-gray-900">
                {item.currentQuantity} {item.unitType === 'box' ? 'boxes' : 'kg'}
              </div>
            )
          },
          {
            key: 'lastActivity',
            label: 'Last Activity',
            mobileLabel: 'Activity',
            render: (_, item) => {
              const lastArrival = item.vehicleArrivals.length > 0 ? item.vehicleArrivals[0] : null;
              const lastSale = item.salesOrders.length > 0 ? item.salesOrders[0] : null;
              
              let activityText = 'No activity';
              let sourceText = '';
              
              if (!lastArrival && !lastSale) {
                activityText = 'No activity';
              } else if (!lastArrival) {
                activityText = `Sale: ${formatDateTime(lastSale!.orderDate)}`;
                sourceText = `To: ${lastSale!.customerName}`;
              } else if (!lastSale) {
                activityText = `Arrival: ${formatDateTime(lastArrival.arrivalTime)}`;
                sourceText = `From: ${lastArrival.supplier}`;
              } else {
                const arrivalDate = new Date(lastArrival.arrivalTime);
                const saleDate = new Date(lastSale.orderDate);
                
                if (arrivalDate > saleDate) {
                  activityText = `Arrival: ${formatDateTime(lastArrival.arrivalTime)}`;
                  sourceText = `From: ${lastArrival.supplier}`;
                } else {
                  activityText = `Sale: ${formatDateTime(lastSale.orderDate)}`;
                  sourceText = `To: ${lastSale.customerName}`;
                }
              }
              
              return (
                <div>
                  <div className="text-sm text-gray-900">{activityText}</div>
                  <div className="text-sm text-gray-500">{sourceText}</div>
                </div>
              );
            }
          },
          {
            key: 'actions',
            label: 'Actions',
            mobileLabel: 'Actions',
            render: (_, item) => (
              <div className="flex flex-col sm:flex-row gap-2">
                <button
                  onClick={() => handleViewHistory(item)}
                  className="text-green-600 hover:text-green-900 text-sm font-medium"
                >
                  View History
                </button>
                
                {/* Inventory Adjustments Dropdown for each item */}
                <div className="relative adjustment-dropdown-container">
                  <button
                    onClick={(e) => handleAdjustClick(item, e)}
                    className="text-blue-600 hover:text-blue-900 text-sm font-medium flex items-center"
                  >
                    <Settings className="h-3 w-3 mr-1" />
                    Adjust
                    <ChevronDown className="h-3 w-3 ml-1" />
                  </button>
                </div>
              </div>
            )
          }
        ]}
        data={filteredItems}
        loading={loading}
        emptyState={
          <div className="py-12 text-center text-gray-500">
            <Package className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Inventory Items Found</h3>
            <p className="text-sm text-gray-500">
              {inventoryItems.length === 0 
                ? "No vehicle arrivals found. Inventory will be populated automatically when vehicle arrivals are recorded."
                : "No items match your current search and filter criteria."
              }
            </p>
          </div>
        }
      />

      {/* History Modal */}
      {showHistoryModal && selectedItem && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-2 sm:p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[95vh] sm:max-h-[90vh] overflow-hidden flex flex-col">
            <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200 flex-shrink-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0 pr-4">
                  <h3 className="text-base sm:text-lg font-medium text-gray-900 break-words">
                    History for {selectedItem.productName}
                  </h3>
                  <p className="text-sm text-gray-500 mt-1">
                    SKU: {selectedItem.skuCode}
                  </p>
                </div>
                <button
                  onClick={() => setShowHistoryModal(false)}
                  className="text-gray-400 hover:text-gray-500 p-1 flex-shrink-0"
                >
                  <span className="sr-only">Close</span>
                  <X className="h-5 w-5 sm:h-6 sm:w-6" />
                </button>
              </div>
            </div>

            <div className="px-4 sm:px-6 py-4 flex-1 overflow-y-auto">
              {(() => {
                const timelineEntries = createTimelineEntries(selectedItem);
                
                if (timelineEntries.length === 0) {
                  return (
                    <div className="text-center py-8 sm:py-12">
                      <Calendar className="h-10 w-10 sm:h-12 sm:w-12 mx-auto text-gray-300 mb-4" />
                      <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">No History Available</h3>
                      <p className="text-sm text-gray-500">
                        No arrivals or sales recorded for this inventory item yet.
                      </p>
                    </div>
                  );
                }

                return (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4 flex flex-col sm:flex-row sm:items-center">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2" />
                        Inventory Timeline
                      </div>
                      <span className="text-sm font-normal text-gray-500 sm:ml-2 mt-1 sm:mt-0">
                        ({timelineEntries.length} {timelineEntries.length === 1 ? 'entry' : 'entries'})
                      </span>
                    </h4>
                    
                    <div className="relative">
                      {/* Timeline line - hidden on very small screens */}
                      <div className="absolute left-5 sm:left-6 top-0 bottom-0 w-0.5 bg-gray-200 hidden xs:block"></div>
                      
                      <div className="space-y-3 sm:space-y-4">
                        {timelineEntries.map((entry, index) => {
                          // Calculate running balance after this transaction
                          let runningBalance = selectedItem.currentQuantity;
                          for (let i = 0; i < index; i++) {
                            const prevEntry = timelineEntries[i];
                            if (prevEntry.type === 'arrival') {
                              runningBalance -= prevEntry.quantity;
                            } else {
                              runningBalance += prevEntry.quantity;
                            }
                          }
                          
                          return (
                            <div key={entry.id} className="relative flex items-start">
                              {/* Timeline dot */}
                              <div className={`flex-shrink-0 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center z-10 ${
                                entry.type === 'arrival' 
                                  ? 'bg-green-100 text-green-600' 
                                  : 'bg-red-100 text-red-600'
                              }`}>
                                {entry.type === 'arrival' ? (
                                  <Plus className="h-4 w-4 sm:h-5 sm:w-5" />
                                ) : (
                                  <Minus className="h-4 w-4 sm:h-5 sm:w-5" />
                                )}
                              </div>
                              
                              {/* Timeline content */}
                              <div className="ml-3 sm:ml-4 flex-1 min-w-0">
                                <div className={`rounded-lg p-3 sm:p-4 shadow-sm border ${
                                  entry.type === 'arrival' 
                                    ? 'bg-green-50 border-green-200' 
                                    : 'bg-red-50 border-red-200'
                                }`}>
                                  <div className="flex flex-col space-y-2 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between mb-2">
                                    <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0">
                                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium w-fit ${
                                        entry.type === 'arrival' 
                                          ? 'bg-green-100 text-green-800' 
                                          : 'bg-red-100 text-red-800'
                                      }`}>
                                        {entry.type === 'arrival' ? (
                                          <>
                                            <Truck className="h-3 w-3 mr-1" />
                                            Arrival
                                          </>
                                        ) : (
                                          <>
                                            <ShoppingCart className="h-3 w-3 mr-1" />
                                            Sale
                                          </>
                                        )}
                                      </span>
                                      <div className="flex flex-col sm:flex-row sm:items-center sm:ml-2 text-sm">
                                        <span className="font-medium text-gray-900">
                                          {entry.type === 'arrival' ? '+' : '-'}{entry.quantity} {selectedItem.unitType === 'box' ? 'boxes' : 'kg'}
                                        </span>
                                        <span className="font-semibold text-gray-600 sm:ml-2">
                                          (Balance: {runningBalance} {selectedItem.unitType === 'box' ? 'boxes' : 'kg'})
                                        </span>
                                      </div>
                                    </div>
                                    <div className="text-xs sm:text-sm text-gray-500">
                                      {formatDateTime(entry.date)}
                                    </div>
                                  </div>
                                  
                                  <div className="grid grid-cols-1 gap-2 sm:gap-3 text-sm">
                                    <div>
                                      <span className="font-medium text-gray-700">
                                        {entry.type === 'arrival' ? 'From:' : 'To:'}
                                      </span>
                                      <div className="text-gray-900 break-words">{entry.source}</div>
                                    </div>
                                    {entry.type === 'sale' && (
                                      <div>
                                        <span className="font-medium text-gray-700">Order:</span>
                                        <div className="text-gray-900 break-words">{entry.details}</div>
                                      </div>
                                    )}
                                    {entry.type === 'arrival' && (
                                      <div>
                                        <span className="font-medium text-gray-700">Purchase Record:</span>
                                        <div className="text-gray-900 break-words">{entry.details}</div>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                );
              })()}
            </div>
          </div>
        </div>
      )}

      {/* Information Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-3 sm:p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <Package className="h-4 w-4 sm:h-5 sm:w-5 text-blue-400" />
          </div>
          <div className="ml-3 min-w-0 flex-1">
            <h3 className="text-sm font-medium text-blue-800">
              Inventory Management Information
            </h3>
            <div className="mt-2 text-xs sm:text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li>Current Stock shows the actual available inventory after sales</li>
                <li>Inventory is automatically updated when vehicle arrivals are completed or sales are made</li>
                <li>Click on any row to view detailed arrival history for that item</li>
                <li>Use the refresh button to update inventory with the latest data</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Inventory Adjustment Modals */}
      <MarkDumpModal
        isOpen={showMarkDumpModal}
        onClose={() => setShowMarkDumpModal(false)}
        item={selectedAdjustmentItem}
        onSuccess={() => {
          loadInventoryData();
          setSelectedAdjustmentItem(null);
        }}
      />

      <ManualAdjustmentModal
        isOpen={showManualAdjustmentModal}
        onClose={() => setShowManualAdjustmentModal(false)}
        item={selectedAdjustmentItem}
        onSuccess={() => {
          loadInventoryData();
          setSelectedAdjustmentItem(null);
        }}
      />

      <UpdateSKUModal
        isOpen={showUpdateSKUModal}
        onClose={() => setShowUpdateSKUModal(false)}
        item={selectedAdjustmentItem}
        onSuccess={() => {
          loadInventoryData();
          setSelectedAdjustmentItem(null);
        }}
      />

      {/* Portal Dropdown */}
      <PortalDropdown />
    </div>
  );
};

export default Inventory;
