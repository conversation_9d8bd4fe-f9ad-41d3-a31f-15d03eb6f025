import React, { useState } from 'react';
import { 
  CreditCard, 
  Plus, 
  ChevronDown, 
  DollarSign, 
  Receipt,
  Download,
  CheckSquare,
  Square,
  Trash2,
  Eye,
  Edit,
  Paperclip
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { usePayments } from '../../hooks/usePayments';
import { usePaymentFilters } from '../../hooks/usePaymentFilters';
import PaymentSummaryCards from '../../components/payments/PaymentSummaryCards';
import PaymentFilters from '../../components/payments/PaymentFilters';
import PaymentFormModal from '../../components/modals/PaymentFormModal';
import { Payment } from '../../types/payment.types';

const PaymentsNew: React.FC = () => {
  const {
    payments,
    loading,
    selectedPayments,
    createPayment,
    updatePayment,
    deletePayment,
    deleteMultiplePayments,
    togglePaymentSelection,
    selectAllPayments,
    clearSelection,
    getPaymentSummary
  } = usePayments();

  const {
    filters,
    filteredPayments,
    activeFiltersCount,
    hasActiveFilters,
    filterOptions,
    updateFilter,
    updateDateRange,
    updateAmountRange,
    clearFilters,
    setQuickDateFilter
  } = usePaymentFilters(payments);

  const [showModal, setShowModal] = useState(false);
  const [modalPaymentType, setModalPaymentType] = useState<'received' | 'made' | 'expense'>('received');
  const [showNewPaymentDropdown, setShowNewPaymentDropdown] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null);

  const summary = getPaymentSummary(filteredPayments);

  const handleNewPayment = (type: 'received' | 'made' | 'expense') => {
    setModalPaymentType(type);
    setEditingPayment(null);
    setShowModal(true);
    setShowNewPaymentDropdown(false);
  };

  const handleEditPayment = (payment: Payment) => {
    setEditingPayment(payment);
    setModalPaymentType(payment.type);
    setShowModal(true);
  };

  const handleModalSave = async (paymentData: any, proofFile?: File) => {
    if (editingPayment) {
      const success = await updatePayment(editingPayment.id, paymentData, proofFile);
      if (success) {
        setShowModal(false);
        setEditingPayment(null);
      }
    } else {
      const success = await createPayment(paymentData, proofFile);
      if (success) {
        setShowModal(false);
      }
    }
  };

  const handleDeletePayment = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this payment record?')) {
      await deletePayment(id);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedPayments.length === 0) return;
    
    if (window.confirm(`Are you sure you want to delete ${selectedPayments.length} payment records?`)) {
      await deleteMultiplePayments(selectedPayments);
    }
  };

  const handleSelectAll = () => {
    if (selectedPayments.length === filteredPayments.length) {
      clearSelection();
    } else {
      selectAllPayments(filteredPayments.map(p => p.id));
    }
  };

  const exportPayments = () => {
    // Simple CSV export
    const headers = ['Date', 'Type', 'Amount', 'Party', 'Mode', 'Status', 'Reference', 'Notes'];
    const csvData = filteredPayments.map(payment => [
      new Date(payment.payment_date).toLocaleDateString(),
      payment.type,
      payment.amount,
      payment.party_name || '',
      payment.mode,
      payment.status,
      payment.reference_number || '',
      payment.notes || ''
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${field}"`).join(','))
      .join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `payments-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    toast.success('Payments exported successfully!');
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'received':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'made':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'expense':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatModeDisplay = (mode: string) => {
    switch (mode) {
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'upi':
        return 'UPI';
      default:
        return mode.charAt(0).toUpperCase() + mode.slice(1);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <CreditCard className="h-6 w-6 text-green-600 mr-2" />
            <h1 className="text-2xl font-bold text-gray-800">Payments</h1>
          </div>
        </div>
        <PaymentSummaryCards summary={summary} loading={true} />
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <div className="flex items-center justify-center">
            <div className="text-gray-500">Loading payments...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center">
          <CreditCard className="h-6 w-6 text-green-600 mr-2" />
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Payments</h1>
          <span className="ml-3 bg-gray-100 text-gray-600 text-sm font-medium px-2 py-1 rounded-full">
            {filteredPayments.length} records
          </span>
        </div>
        
        <div className="flex items-center gap-2 sm:gap-3">
          {/* Export Button */}
          <button
            onClick={exportPayments}
            className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Download className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Export</span>
          </button>

          {/* New Payment Dropdown */}
          <div className="relative">
            <button 
              onClick={() => setShowNewPaymentDropdown(!showNewPaymentDropdown)}
              className="bg-green-600 text-white rounded-lg px-3 sm:px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200 flex items-center"
            >
              <Plus className="h-4 w-4 sm:mr-1" />
              <span className="hidden sm:inline">New Payment</span>
              <ChevronDown className="h-4 w-4 ml-1" />
            </button>
            
            {showNewPaymentDropdown && (
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                <div className="py-1">
                  <button
                    onClick={() => handleNewPayment('received')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <DollarSign className="h-4 w-4 mr-3 text-green-600" />
                    Payment Received
                  </button>
                  <button
                    onClick={() => handleNewPayment('made')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <CreditCard className="h-4 w-4 mr-3 text-blue-600" />
                    Payment Made
                  </button>
                  <button
                    onClick={() => handleNewPayment('expense')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Receipt className="h-4 w-4 mr-3 text-red-600" />
                    Record Expense
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <PaymentSummaryCards summary={summary} loading={loading} />

      {/* Filters */}
      <PaymentFilters
        filters={filters}
        onUpdateFilter={updateFilter}
        onUpdateDateRange={updateDateRange}
        onUpdateAmountRange={updateAmountRange}
        onClearFilters={clearFilters}
        onSetQuickDateFilter={setQuickDateFilter}
        activeFiltersCount={activeFiltersCount}
        hasActiveFilters={hasActiveFilters}
        filterOptions={filterOptions}
      />

      {/* Bulk Actions */}
      {selectedPayments.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex items-center">
              <span className="text-sm font-medium text-blue-800">
                {selectedPayments.length} payment{selectedPayments.length > 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={handleBulkDelete}
                className="flex items-center px-3 py-1 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Delete Selected</span>
                <span className="sm:hidden">Delete</span>
              </button>
              <button
                onClick={clearSelection}
                className="text-sm font-medium text-blue-600 hover:text-blue-700"
              >
                <span className="hidden sm:inline">Clear Selection</span>
                <span className="sm:hidden">Clear</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payments Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {filteredPayments.length > 0 ? (
          <>
            {/* Desktop Table */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left">
                      <button
                        onClick={handleSelectAll}
                        className="flex items-center text-xs font-medium text-gray-500 uppercase tracking-wider hover:text-gray-700"
                      >
                        {selectedPayments.length === filteredPayments.length ? (
                          <CheckSquare className="h-4 w-4 mr-2" />
                        ) : (
                          <Square className="h-4 w-4 mr-2" />
                        )}
                        Select All
                      </button>
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Details
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount & Type
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Party
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Mode
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reference
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredPayments.map((payment) => (
                    <tr 
                      key={payment.id} 
                      className={`hover:bg-gray-50 transition-colors ${
                        selectedPayments.includes(payment.id) ? 'bg-blue-50' : ''
                      }`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => togglePaymentSelection(payment.id)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          {selectedPayments.includes(payment.id) ? (
                            <CheckSquare className="h-4 w-4 text-blue-600" />
                          ) : (
                            <Square className="h-4 w-4" />
                          )}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-gray-100">
                            {payment.type === 'received' ? (
                              <DollarSign className="h-5 w-5 text-green-600" />
                            ) : payment.type === 'made' ? (
                              <CreditCard className="h-5 w-5 text-blue-600" />
                            ) : (
                              <Receipt className="h-5 w-5 text-red-600" />
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {formatDateTime(payment.payment_date)}
                            </div>
                            <div className="text-sm text-gray-500">
                              {payment.notes && payment.notes.length > 40 
                                ? `${payment.notes.substring(0, 40)}...` 
                                : payment.notes || 'No notes'
                              }
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-semibold ${
                          payment.type === 'received' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {payment.type === 'received' ? '+' : '-'}{formatCurrency(payment.amount)}
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${getTypeColor(payment.type)}`}>
                          {payment.type.charAt(0).toUpperCase() + payment.type.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {payment.party_name || 'N/A'}
                        </div>
                        {payment.party_type && (
                          <div className="text-sm text-gray-500 capitalize">
                            {payment.party_type}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center space-x-2">
                          <div className="text-sm text-gray-900">
                            {formatModeDisplay(payment.mode)}
                          </div>
                          {payment.proof_attachment_url && (
                            <div title={`Proof attached: ${payment.proof_attachment_name || 'File'}`}>
                              <Paperclip className="h-3 w-3 text-gray-500" />
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {payment.reference_number || 'No reference'}
                        </div>
                        {payment.reference_type && (
                          <div className="text-sm text-gray-500">
                            {payment.reference_type.replace('_', ' ')}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <button 
                            onClick={() => handleEditPayment(payment)}
                            className="text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50"
                            title="Edit payment"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button 
                            className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                            title="View details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button 
                            onClick={() => handleDeletePayment(payment.id)}
                            className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                            title="Delete payment"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Card View */}
            <div className="lg:hidden divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <div 
                  key={payment.id} 
                  className={`p-4 hover:bg-gray-50 transition-colors ${
                    selectedPayments.includes(payment.id) ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center">
                      <button
                        onClick={() => togglePaymentSelection(payment.id)}
                        className="text-gray-400 hover:text-gray-600 mr-3"
                      >
                        {selectedPayments.includes(payment.id) ? (
                          <CheckSquare className="h-4 w-4 text-blue-600" />
                        ) : (
                          <Square className="h-4 w-4" />
                        )}
                      </button>
                      <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center rounded-full bg-gray-100">
                        {payment.type === 'received' ? (
                          <DollarSign className="h-5 w-5 text-green-600" />
                        ) : payment.type === 'made' ? (
                          <CreditCard className="h-5 w-5 text-blue-600" />
                        ) : (
                          <Receipt className="h-5 w-5 text-red-600" />
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <button 
                        onClick={() => handleEditPayment(payment)}
                        className="text-indigo-600 hover:text-indigo-900 p-1 rounded hover:bg-indigo-50"
                        title="Edit payment"
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button 
                        className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-50"
                        title="View details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button 
                        onClick={() => handleDeletePayment(payment.id)}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                        title="Delete payment"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="ml-16 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className={`text-lg font-semibold ${
                        payment.type === 'received' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {payment.type === 'received' ? '+' : '-'}{formatCurrency(payment.amount)}
                      </div>
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full border ${getTypeColor(payment.type)}`}>
                        {payment.type.charAt(0).toUpperCase() + payment.type.slice(1)}
                      </span>
                    </div>
                    
                    <div className="text-sm font-medium text-gray-900">
                      {formatDateTime(payment.payment_date)}
                    </div>
                    
                    {payment.party_name && (
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Party:</span> {payment.party_name}
                        {payment.party_type && (
                          <span className="text-gray-500 ml-1 capitalize">({payment.party_type})</span>
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div className="flex items-center">
                        <span className="font-medium">Mode:</span>
                        <span className="ml-1">{formatModeDisplay(payment.mode)}</span>
                        {payment.proof_attachment_url && (
                          <Paperclip className="h-3 w-3 text-gray-500 ml-2" />
                        )}
                      </div>
                      {payment.reference_number && (
                        <div className="text-xs text-gray-500">
                          Ref: {payment.reference_number}
                        </div>
                      )}
                    </div>
                    
                    {payment.notes && (
                      <div className="text-sm text-gray-500">
                        <span className="font-medium">Notes:</span> {payment.notes.length > 60 
                          ? `${payment.notes.substring(0, 60)}...` 
                          : payment.notes
                        }
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="py-12 text-center">
            <CreditCard className="h-12 w-12 mx-auto text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Payment Records Found</h3>
            <p className="text-sm text-gray-500 mb-4">
              {payments.length === 0 
                ? "Get started by recording your first payment transaction."
                : "No payments match your current search and filter criteria."
              }
            </p>
            {payments.length === 0 && (
              <div className="flex justify-center space-x-3">
                <button
                  onClick={() => handleNewPayment('received')}
                  className="bg-green-600 text-white rounded-lg px-4 py-2 text-sm font-medium hover:bg-green-700 transition-colors duration-200"
                >
                  Record Payment Received
                </button>
                <button
                  onClick={() => handleNewPayment('made')}
                  className="bg-blue-600 text-white rounded-lg px-4 py-2 text-sm font-medium hover:bg-blue-700 transition-colors duration-200"
                >
                  Record Payment Made
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Payment Form Modal */}
      <PaymentFormModal
        isOpen={showModal}
        onClose={() => {
          setShowModal(false);
          setEditingPayment(null);
        }}
        paymentType={modalPaymentType}
        onSave={handleModalSave}
        editingPayment={editingPayment}
      />

      {/* Information Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row">
          <div className="flex-shrink-0 mb-3 sm:mb-0 sm:mr-3">
            <CreditCard className="h-5 w-5 text-blue-400" />
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-blue-800 mb-2">
              Payment Management Information
            </h3>
            <div className="text-sm text-blue-700">
              <ul className="list-disc list-inside space-y-1">
                <li><strong>Payment Received:</strong> Record money received from customers for sales orders</li>
                <li><strong>Payment Made:</strong> Record money paid to suppliers for purchase orders</li>
                <li><strong>Expenses:</strong> Record business expenses like transportation, commission, utilities, etc.</li>
                <li>All payment records are automatically included in the general ledger</li>
                <li className="hidden sm:list-item">Use advanced filters to analyze payments for specific periods and criteria</li>
                <li className="hidden sm:list-item">Export functionality available for reporting and analysis</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentsNew;
