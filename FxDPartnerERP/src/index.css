@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile-specific optimizations */
@layer base {
  html {
    /* Prevent zoom on input focus on iOS */
    -webkit-text-size-adjust: 100%;
    /* Improve touch scrolling on iOS */
    -webkit-overflow-scrolling: touch;
  }
  
  body {
    /* Prevent horizontal scroll on mobile */
    overflow-x: hidden;
    /* Improve touch scrolling */
    -webkit-overflow-scrolling: touch;
  }
  
  /* Improve touch targets on mobile */
  button, 
  [role="button"], 
  input[type="submit"], 
  input[type="button"] {
    /* Minimum touch target size */
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px;
  }
  
  /* Improve select dropdown on mobile */
  select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'><path fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 32px;
  }
}

/* Mobile-specific utilities */
@layer utilities {
  /* Safe area insets for devices with notches */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
  
  /* Touch-friendly interactions */
  .touch-manipulation {
    touch-action: manipulation;
  }
  
  /* Prevent text selection on UI elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Smooth scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }
}

/* Mobile-specific component styles */
@layer components {
  /* Mobile-optimized modal */
  .mobile-modal {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }
  
  .mobile-modal-content {
    @apply min-h-screen sm:min-h-0 sm:max-h-[90vh] w-full sm:max-w-lg sm:mx-auto sm:my-8;
  }
  
  /* Mobile-optimized dropdown */
  .mobile-dropdown {
    @apply absolute left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-50;
    @apply sm:left-auto sm:right-0 sm:w-48;
  }
  
  /* Mobile-optimized table */
  .mobile-table-scroll {
    @apply overflow-x-auto -mx-4 px-4 sm:mx-0 sm:px-0;
  }
  
  /* Mobile-optimized form */
  .mobile-form-group {
    @apply space-y-1 mb-4;
  }
  
  .mobile-form-input {
    @apply w-full px-3 py-3 sm:py-2 text-base sm:text-sm border border-gray-300 rounded-md;
    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500;
  }
  
  .mobile-form-button {
    @apply w-full sm:w-auto px-4 py-3 sm:py-2 text-base sm:text-sm font-medium;
    @apply bg-green-600 text-white rounded-md hover:bg-green-700;
    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
    @apply transition-colors duration-200;
  }
}
