import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { organizationService, userService, roleService } from '../services/adminService';
import toast from 'react-hot-toast';

interface Stats {
  organizations: {
    total: number;
    active: number;
    inactive: number;
  };
  users: {
    total: number;
    active: number;
    inactive: number;
    pending: number;
  };
  roles: {
    total: number;
    active: number;
    inactive: number;
  };
}

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const [orgStats, userStats, roleStats] = await Promise.all([
        organizationService.getStats(),
        userService.getStats(),
        roleService.getStats()
      ]);

      setStats({
        organizations: {
          total: orgStats.data.totalOrganizations,
          active: orgStats.data.activeOrganizations,
          inactive: orgStats.data.inactiveOrganizations
        },
        users: {
          total: userStats.data.totalUsers,
          active: userStats.data.activeUsers,
          inactive: userStats.data.inactiveUsers,
          pending: userStats.data.pendingUsers
        },
        roles: {
          total: roleStats.data.totalRoles,
          active: roleStats.data.activeRoles,
          inactive: roleStats.data.inactiveRoles
        }
      });
    } catch (error: any) {
      toast.error('Failed to load dashboard statistics');
      console.error('Dashboard stats error:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Organizations',
      total: stats?.organizations.total || 0,
      active: stats?.organizations.active || 0,
      inactive: stats?.organizations.inactive || 0,
      href: '/admin/organizations',
      icon: (
        <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      ),
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Users',
      total: stats?.users.total || 0,
      active: stats?.users.active || 0,
      inactive: stats?.users.inactive || 0,
      pending: stats?.users.pending || 0,
      href: '/admin/users',
      icon: (
        <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: 'User Roles',
      total: stats?.roles.total || 0,
      active: stats?.roles.active || 0,
      inactive: stats?.roles.inactive || 0,
      href: '/admin/roles',
      icon: (
        <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
      ),
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to Admin Panel</h3>
        <p className="text-gray-600">
          Manage organizations, users, and roles from this central dashboard. 
          Use the navigation menu to access different sections.
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card) => (
          <Link
            key={card.title}
            to={card.href}
            className={`${card.bgColor} ${card.borderColor} border rounded-lg p-6 hover:shadow-md transition-shadow duration-200`}
          >
            <div className="flex items-center justify-between">
              <div>
                <h4 className="text-lg font-semibold text-gray-900">{card.title}</h4>
                <div className="mt-2">
                  <div className="text-3xl font-bold text-gray-900">{card.total}</div>
                  <div className="mt-2 space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Active:</span>
                      <span className="font-medium text-green-600">{card.active}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Inactive:</span>
                      <span className="font-medium text-red-600">{card.inactive}</span>
                    </div>
                    {card.pending !== undefined && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Pending:</span>
                        <span className="font-medium text-yellow-600">{card.pending}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex-shrink-0">
                {card.icon}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Link
            to="/admin/organizations"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <svg className="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <div>
              <div className="font-medium text-gray-900">Manage Organizations</div>
              <div className="text-sm text-gray-500">Create, edit, and manage organizations</div>
            </div>
          </Link>

          <Link
            to="/admin/users"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <svg className="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <div>
              <div className="font-medium text-gray-900">Manage Users</div>
              <div className="text-sm text-gray-500">View and edit user accounts</div>
            </div>
          </Link>

          <Link
            to="/admin/roles"
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
          >
            <svg className="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <div className="font-medium text-gray-900">Manage Roles</div>
              <div className="text-sm text-gray-500">Configure user roles and permissions</div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
