# FxD Partner ERP Frontend - Cline Rules

## Project Overview
This is a React 18 + TypeScript + Vite application for an ERP system with multi-organization support, role-based permissions, and an admin panel. The frontend uses Tailwind CSS with Radix UI components and follows a context-based state management pattern.

## Technology Stack
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + Radix UI components
- **Routing**: React Router v6
- **State Management**: React Context API
- **HTTP Client**: Custom API service with fetch
- **UI Components**: Custom components with Radix UI primitives
- **Icons**: Lucide React
- **Notifications**: React Hot Toast

## Code Style & Conventions

### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use proper typing for props, state, and API responses
- Prefer type unions over enums where appropriate
- Use generic types for reusable components

### React Components
- Use functional components with hooks
- Follow PascalCase for component names
- Use descriptive prop interfaces
- Implement proper error boundaries
- Use React.memo for performance optimization when needed

### File Organization
```
src/
├── components/          # Reusable UI components
├── pages/              # Route-specific page components
├── contexts/           # React Context providers
├── hooks/              # Custom React hooks
├── services/           # API and external service integrations
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── layouts/            # Layout components
└── admin/              # Admin-specific components and logic
```

### Component Structure
- Keep components focused and single-responsibility
- Extract complex logic into custom hooks
- Use composition over inheritance
- Implement loading and error states consistently

### State Management
- Use React Context for global state (Auth, Sidebar, Admin)
- Use useState for local component state
- Use useEffect for side effects and cleanup
- Implement proper dependency arrays in useEffect

### API Integration
- Use the centralized API service in `src/services/api/`
- Always include organization headers for multi-tenant requests
- Implement proper error handling and loading states
- Use consistent response data structures

### Authentication & Authorization
- Use AuthContext for user authentication state
- Implement PrivateRoute components for protected routes
- Check permissions using context methods: `hasPermission()`, `hasRole()`, `canAccessPage()`
- Handle organization switching properly

### Routing
- Use React Router v6 patterns
- Implement nested routes for dashboard layout
- Use proper route guards (PrivateRoute, AdminRoute)
- Handle 404 pages gracefully

### Styling Guidelines
- Use Tailwind CSS utility classes
- Follow mobile-first responsive design
- Use consistent spacing and color schemes
- Implement dark mode support where applicable
- Use Radix UI components for complex interactions

## Responsive Design Guidelines

### Mobile-First Approach
Always design for mobile devices first, then enhance for larger screens using Tailwind's responsive prefixes:
- `sm:` - Small devices (640px+)
- `md:` - Medium devices (768px+) 
- `lg:` - Large devices (1024px+)
- `xl:` - Extra large devices (1280px+)

### Responsive Layout Patterns

#### 1. Header/Navigation
```tsx
// Mobile: Stacked layout, Desktop: Side-by-side
<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
  <div className="flex items-center">
    <Icon className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 mr-2" />
    <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Page Title</h1>
  </div>
  <button className="w-full sm:w-auto px-3 py-2 sm:px-4 sm:py-2">
    Action Button
  </button>
</div>
```

#### 2. Summary Cards/Grid
```tsx
// Mobile: 2 columns, Desktop: 4 columns
<div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
  <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm">
    <div className="flex justify-between items-start">
      <div className="min-w-0 flex-1">
        <p className="text-xs sm:text-sm font-medium text-gray-500 truncate">Label</p>
        <p className="text-lg sm:text-2xl font-bold text-gray-800">Value</p>
      </div>
      <div className="h-8 w-8 sm:h-10 sm:w-10 flex items-center justify-center rounded-full bg-green-100 text-green-600 flex-shrink-0 ml-2">
        <Icon className="h-4 w-4 sm:h-5 sm:w-5" />
      </div>
    </div>
  </div>
</div>
```

#### 3. Search/Filter Bar
```tsx
// Full width on mobile, constrained on desktop
<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
  <div className="relative w-full sm:flex-1 sm:max-w-xs">
    <input className="block w-full pl-10 pr-3 py-2 text-sm sm:text-base" />
  </div>
</div>
```

#### 4. Data Display - Dual View System
```tsx
{/* Desktop Table View */}
<div className="hidden lg:block">
  <table className="min-w-full divide-y divide-gray-200">
    {/* Table content */}
  </table>
</div>

{/* Mobile Card View */}
<div className="lg:hidden">
  <div className="divide-y divide-gray-200">
    {items.map((item) => (
      <div key={item.id} className="p-4 hover:bg-gray-50">
        {/* Card content with consistent layout */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-medium text-gray-900 truncate mb-1">
              {item.title}
            </h3>
            <p className="text-xs text-gray-500">{item.subtitle}</p>
          </div>
          <div className="flex items-center space-x-1 ml-2">
            {/* Action buttons */}
          </div>
        </div>
        
        {/* Status - Consistent Position */}
        <div className="mb-3">
          <span className="px-2 py-1 text-xs font-semibold rounded-full bg-status-color">
            {item.status}
          </span>
        </div>
        
        {/* Additional content sections */}
      </div>
    ))}
  </div>
</div>
```

#### 5. Modal/Dialog Responsive
```tsx
<div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full mx-4 sm:mx-0">
  <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
    <h3 className="text-base sm:text-lg leading-6 font-medium text-gray-900">
      Modal Title
    </h3>
  </div>
</div>
```

### Responsive Design Best Practices

#### Typography
- Use responsive text sizes: `text-xs sm:text-sm`, `text-sm sm:text-base`, `text-lg sm:text-xl`
- Ensure proper line heights and spacing across devices
- Use `truncate` for long text that might overflow on mobile

#### Spacing
- Use responsive spacing: `space-y-3 sm:space-y-0`, `gap-3 sm:gap-4`
- Adjust padding/margins: `p-3 sm:p-4`, `px-3 py-2 sm:px-4 sm:py-2`
- Use `mb-3` for consistent mobile spacing, adjust for desktop as needed

#### Touch Targets
- Ensure buttons are at least 44px (p-2 minimum) for touch interaction
- Use proper spacing between interactive elements: `space-x-1` for mobile, `space-x-2` for desktop
- Add hover states that work on both desktop and mobile: `hover:bg-gray-50`

#### Layout Consistency
- Always position status badges in the same location across all records
- Use consistent information hierarchy: Header → Status → Details
- Maintain proper alignment with `items-start`, `items-center` as appropriate

#### Performance Considerations
- Use conditional rendering (`hidden lg:block`, `lg:hidden`) to avoid rendering unused content
- Implement proper image sizing and loading for different screen sizes
- Consider lazy loading for mobile to improve performance

#### Common Responsive Patterns
```tsx
// Button sizing
className="w-full sm:w-auto"

// Icon sizing
className="h-4 w-4 sm:h-5 sm:w-5"

// Container sizing
className="h-8 w-8 sm:h-10 sm:w-10"

// Text truncation
className="truncate"

// Flexible layouts
className="flex-1 min-w-0"

// Responsive grids
className="grid grid-cols-2 lg:grid-cols-4"

// Conditional display
className="hidden sm:block" // Show only on desktop
className="sm:hidden" // Show only on mobile
```

### Testing Responsive Design
- Test on actual devices, not just browser dev tools
- Verify touch interactions work properly
- Check text readability at different sizes
- Ensure all interactive elements are accessible
- Test landscape and portrait orientations on mobile

### Form Handling
- Use controlled components for forms
- Implement proper validation
- Show loading states during submission
- Handle form errors gracefully
- Use consistent form layouts

### Error Handling
- Implement error boundaries for component trees
- Use try-catch blocks for async operations
- Show user-friendly error messages
- Log errors appropriately for debugging

### Performance
- Use React.memo for expensive components
- Implement proper key props for lists
- Lazy load routes and components when appropriate
- Optimize bundle size with proper imports

## Multi-Organization Support
- Always include organization context in API requests
- Handle organization switching in AuthContext
- Update API configuration when organization changes
- Ensure UI reflects current organization state

## Admin Panel
- Separate admin components in `/admin` directory
- Use AdminContext for admin-specific state
- Implement AdminRoute for admin-only access
- Follow consistent admin UI patterns

## Testing Considerations
- Write unit tests for utility functions
- Test component rendering and interactions
- Mock API calls in tests
- Test authentication and authorization flows

## Common Patterns

### API Request Pattern
```typescript
const fetchData = async () => {
  try {
    setLoading(true);
    const response = await apiRequest('/endpoint');
    setData(response);
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
};
```

### Permission Check Pattern
```typescript
const { hasPermission, canAccessPage } = useAuth();

if (!hasPermission('read_sales')) {
  return <AccessDenied />;
}
```

### Form Component Pattern
```typescript
const [formData, setFormData] = useState(initialState);
const [loading, setLoading] = useState(false);
const [errors, setErrors] = useState({});

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  // Handle form submission
};
```

## Best Practices
- Always handle loading and error states
- Use semantic HTML elements
- Implement proper accessibility features
- Follow React hooks rules
- Keep components small and focused
- Use TypeScript strictly
- Implement proper error boundaries
- Handle edge cases gracefully
- Use consistent naming conventions
- Document complex logic with comments
- **Ensure all new components are mobile responsive** - Use Tailwind's responsive utilities (sm:, md:, lg:, xl:) 

## Avoid
- Direct DOM manipulation
- Inline styles (use Tailwind classes)
- Large monolithic components
- Prop drilling (use Context when appropriate)
- Mutating state directly
- Missing error handling
- Hardcoded strings (use constants)
- Inconsistent file naming
- Missing TypeScript types
- Unused imports and variables
