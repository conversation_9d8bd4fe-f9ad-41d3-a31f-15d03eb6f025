# FxD Partner ERP Frontend - Cline Rules

## Project Overview
This is a React 18 + TypeScript + Vite application for an ERP system with multi-organization support, role-based permissions, and an admin panel. The frontend uses Tailwind CSS with Radix UI components and follows a context-based state management pattern.

## Technology Stack
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS + Radix UI components
- **Routing**: React Router v6
- **State Management**: React Context API
- **HTTP Client**: Custom API service with fetch
- **UI Components**: Custom components with Radix UI primitives
- **Icons**: Lucide React
- **Notifications**: React Hot Toast

## Code Style & Conventions

### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Use proper typing for props, state, and API responses
- Prefer type unions over enums where appropriate
- Use generic types for reusable components

### React Components
- Use functional components with hooks
- Follow PascalCase for component names
- Use descriptive prop interfaces
- Implement proper error boundaries
- Use React.memo for performance optimization when needed

### File Organization
```
src/
├── components/          # Reusable UI components
├── pages/              # Route-specific page components
├── contexts/           # React Context providers
├── hooks/              # Custom React hooks
├── services/           # API and external service integrations
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
├── layouts/            # Layout components
└── admin/              # Admin-specific components and logic
```

### Component Structure
- Keep components focused and single-responsibility
- Extract complex logic into custom hooks
- Use composition over inheritance
- Implement loading and error states consistently

### State Management
- Use React Context for global state (Auth, Sidebar, Admin)
- Use useState for local component state
- Use useEffect for side effects and cleanup
- Implement proper dependency arrays in useEffect

### API Integration
- Use the centralized API service in `src/services/api/`
- Always include organization headers for multi-tenant requests
- Implement proper error handling and loading states
- Use consistent response data structures

### Authentication & Authorization
- Use AuthContext for user authentication state
- Implement PrivateRoute components for protected routes
- Check permissions using context methods: `hasPermission()`, `hasRole()`, `canAccessPage()`
- Handle organization switching properly

### Routing
- Use React Router v6 patterns
- Implement nested routes for dashboard layout
- Use proper route guards (PrivateRoute, AdminRoute)
- Handle 404 pages gracefully

### Styling Guidelines
- Use Tailwind CSS utility classes
- Follow mobile-first responsive design
- Use consistent spacing and color schemes
- Implement dark mode support where applicable
- Use Radix UI components for complex interactions

### Form Handling
- Use controlled components for forms
- Implement proper validation
- Show loading states during submission
- Handle form errors gracefully
- Use consistent form layouts

### Error Handling
- Implement error boundaries for component trees
- Use try-catch blocks for async operations
- Show user-friendly error messages
- Log errors appropriately for debugging

### Performance
- Use React.memo for expensive components
- Implement proper key props for lists
- Lazy load routes and components when appropriate
- Optimize bundle size with proper imports

## Multi-Organization Support
- Always include organization context in API requests
- Handle organization switching in AuthContext
- Update API configuration when organization changes
- Ensure UI reflects current organization state

## Admin Panel
- Separate admin components in `/admin` directory
- Use AdminContext for admin-specific state
- Implement AdminRoute for admin-only access
- Follow consistent admin UI patterns

## Testing Considerations
- Write unit tests for utility functions
- Test component rendering and interactions
- Mock API calls in tests
- Test authentication and authorization flows

## Common Patterns

### API Request Pattern
```typescript
const fetchData = async () => {
  try {
    setLoading(true);
    const response = await apiRequest('/endpoint');
    setData(response);
  } catch (error) {
    setError(error.message);
  } finally {
    setLoading(false);
  }
};
```

### Permission Check Pattern
```typescript
const { hasPermission, canAccessPage } = useAuth();

if (!hasPermission('read_sales')) {
  return <AccessDenied />;
}
```

### Form Component Pattern
```typescript
const [formData, setFormData] = useState(initialState);
const [loading, setLoading] = useState(false);
const [errors, setErrors] = useState({});

const handleSubmit = async (e: React.FormEvent) => {
  e.preventDefault();
  // Handle form submission
};
```

## Best Practices
- Always handle loading and error states
- Use semantic HTML elements
- Implement proper accessibility features
- Follow React hooks rules
- Keep components small and focused
- Use TypeScript strictly
- Implement proper error boundaries
- Handle edge cases gracefully
- Use consistent naming conventions
- Document complex logic with comments

## Avoid
- Direct DOM manipulation
- Inline styles (use Tailwind classes)
- Large monolithic components
- Prop drilling (use Context when appropriate)
- Mutating state directly
- Missing error handling
- Hardcoded strings (use constants)
- Inconsistent file naming
- Missing TypeScript types
- Unused imports and variables
