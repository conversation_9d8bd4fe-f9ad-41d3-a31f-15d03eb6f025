-- Make organization_id nullable in users table to allow simple user creation
-- Users can still belong to multiple organizations via the user_organizations junction table

ALTER TABLE users MODIFY COLUMN organization_id VARCHAR(36) NULL;

-- Update the foreign key constraint to handle nullable organization_id
ALTER TABLE users DROP FOREIGN KEY users_ibfk_1;
ALTER TABLE users ADD CONSTRAINT fk_users_organization_id 
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE SET NULL;
