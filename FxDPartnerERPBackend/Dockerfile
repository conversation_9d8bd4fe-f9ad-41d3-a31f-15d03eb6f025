FROM node:18-alpine

WORKDIR /app

# Copy package files 
COPY package*.json ./

# Install bcryptjs instead of bcrypt to avoid architecture issues
RUN npm uninstall bcrypt --no-save || true && \
    npm install bcryptjs --save && \
    npm install

# Copy the rest of the application code
COPY . .

# Patch any code that uses bcrypt to use bcryptjs instead
RUN find ./src -type f -name "*.ts" -exec sed -i "s/from .\/bcrypt./from 'bcryptjs'/g" {} \; || true
RUN find ./src -type f -name "*.ts" -exec sed -i "s/require(.\/bcrypt.)/require('bcryptjs')/g" {} \; || true
RUN find ./src -type f -name "*.ts" -exec sed -i "s/from 'bcrypt'/from 'bcryptjs'/g" {} \; || true
RUN find ./src -type f -name "*.ts" -exec sed -i "s/require('bcrypt')/require('bcryptjs')/g" {} \; || true

# Build TypeScript code
RUN export $(cat .env | xargs) && npm run build

# Expose the backend port
EXPOSE 3000
EXPOSE 3001
# Command to run the application using shell to execute the script
CMD ["npm", "start"]