import { Router } from 'express';
import {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  updateCustomerBalance,
  getSalesOrdersByCustomerId,
  getPaymentsByCustomerId
} from '../controllers/customerController';
import {
  getSuppliers,
  getSupplier,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  updateSupplierBalance,
  getPurchaseRecordsBySupplierId,
  getPaymentsBySupplierId
} from '../controllers/supplierController';

const router = Router();

// Customer routes
router.get('/customers', getCustomers);
router.get('/customers/:id', getCustomer);
router.post('/customers', createCustomer);
router.put('/customers/:id', updateCustomer);
router.delete('/customers/:id', deleteCustomer);
router.patch('/customers/:id/balance', updateCustomerBalance);
router.get('/customers/:id/sales-orders', getSalesOrdersByCustomerId);
router.get('/customers/:id/payments', getPaymentsByCustomerId);

// Supplier routes
router.get('/suppliers', getSuppliers);
router.get('/suppliers/:id', getSupplier);
router.post('/suppliers', createSupplier);
router.put('/suppliers/:id', updateSupplier);
router.delete('/suppliers/:id', deleteSupplier);
router.patch('/suppliers/:id/balance', updateSupplierBalance);
router.get('/suppliers/:id/purchase-records', getPurchaseRecordsBySupplierId);
router.get('/suppliers/:id/payments', getPaymentsBySupplierId);

export default router;
