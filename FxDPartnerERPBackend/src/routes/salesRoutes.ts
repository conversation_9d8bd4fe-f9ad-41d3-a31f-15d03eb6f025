import express from 'express';
const router = express.Router();
import {
  getSalesOrders,
  getSalesOrder,
  getSalesOrdersByCustomer,
  createSalesOrder,
  updateSalesOrder,
  cancelSalesOrder,
  deleteSalesOrder,
  checkInventoryForSalesOrder,
  createSalesOrderWithMultiplePayments,
  updateSalesOrderDispatchDetails,
  getOutstationSalesOrders,
  approvePendingSalesOrder,
  rejectPendingSalesOrder,
  testSalesOrderCreation,
  getSalesAnalytics
} from '../controllers/salesController';

// GET /api/sales - Get all sales orders
router.get('/', getSalesOrders);

// GET /api/sales/analytics - Get sales analytics (must be before /:id)
router.get('/analytics', getSalesAnalytics);

// GET /api/sales/outstation - Get outstation sales orders (must be before /:id)
router.get('/outstation', getOutstationSalesOrders);

// GET /api/sales/customer/:customerId - Get sales orders by customer
router.get('/customer/:customerId', getSalesOrdersByCustomer);

// GET /api/sales/:id - Get single sales order
router.get('/:id', getSalesOrder);

// POST /api/sales - Create new sales order
router.post('/', createSalesOrder);

// PUT /api/sales/:id - Update sales order
router.put('/:id', updateSalesOrder);

// PATCH /api/sales/:id/cancel - Cancel sales order
router.patch('/:id/cancel', cancelSalesOrder);

// DELETE /api/sales/:id - Delete sales order
router.delete('/:id', deleteSalesOrder);

// POST /api/sales/check-inventory - Check inventory for sales order
router.post('/check-inventory', checkInventoryForSalesOrder);

// POST /api/sales/with-payments - Create sales order with multiple payments
router.post('/with-payments', createSalesOrderWithMultiplePayments);

// POST /api/sales/test - Test sales order creation (debug endpoint)
router.post('/test', testSalesOrderCreation);

// PATCH /api/sales/:id/dispatch - Update dispatch details
router.patch('/:id/dispatch', updateSalesOrderDispatchDetails);


// PATCH /api/sales/:id/approve - Approve pending sales order
router.patch('/:id/approve', approvePendingSalesOrder);

// PATCH /api/sales/:id/reject - Reject pending sales order
router.patch('/:id/reject', rejectPendingSalesOrder);

export default router;
