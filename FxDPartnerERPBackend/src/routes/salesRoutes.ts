import express from 'express';
const router = express.Router();
import {
  getSalesOrders,
  getSalesOrder,
  getSalesOrdersByCustomer,
  createSalesOrder,
  updateSalesOrder,
  cancelSalesOrder,
  deleteSalesOrder,
  checkInventoryForSalesOrder,
  createSalesOrderWithMultiplePayments,
  updateSalesOrderDispatchDetails,
  getOutstationSalesOrders,
  approvePendingSalesOrder,
  rejectPendingSalesOrder,
  testSalesOrderCreation,
  getSalesAnalytics
} from '../controllers/salesController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for sales orders
// Page name: 'sales'

// GET /api/sales - Get all sales orders
router.get('/', requirePageView('sales'), getSalesOrders);

// GET /api/sales/analytics - Get sales analytics (must be before /:id)
router.get('/analytics', requirePageView('sales'), getSalesAnalytics);

// GET /api/sales/outstation - Get outstation sales orders (must be before /:id)
router.get('/outstation', requirePageView('sales'), getOutstationSalesOrders);

// GET /api/sales/customer/:customerId - Get sales orders by customer
router.get('/customer/:customerId', requirePageView('sales'), getSalesOrdersByCustomer);

// GET /api/sales/:id - Get single sales order
router.get('/:id', requirePageView('sales'), getSalesOrder);

// POST /api/sales - Create new sales order
router.post('/', requirePageEdit('sales'), createSalesOrder);

// PUT /api/sales/:id - Update sales order
router.put('/:id', requirePageEdit('sales'), updateSalesOrder);

// PATCH /api/sales/:id/cancel - Cancel sales order
router.patch('/:id/cancel', requirePageEdit('sales'), cancelSalesOrder);

// DELETE /api/sales/:id - Delete sales order
router.delete('/:id', requirePageEdit('sales'), deleteSalesOrder);

// POST /api/sales/check-inventory - Check inventory for sales order
router.post('/check-inventory', requirePageView('sales'), checkInventoryForSalesOrder);

// POST /api/sales/with-payments - Create sales order with multiple payments
router.post('/with-payments', requirePageEdit('sales'), createSalesOrderWithMultiplePayments);

// POST /api/sales/test - Test sales order creation (debug endpoint)
router.post('/test', requirePageEdit('sales'), testSalesOrderCreation);

// PATCH /api/sales/:id/dispatch - Update dispatch details
router.patch('/:id/dispatch', requirePageEdit('sales'), updateSalesOrderDispatchDetails);

// PATCH /api/sales/:id/approve - Approve pending sales order
router.patch('/:id/approve', requirePageEdit('sales'), approvePendingSalesOrder);

// PATCH /api/sales/:id/reject - Reject pending sales order
router.patch('/:id/reject', requirePageEdit('sales'), rejectPendingSalesOrder);

export default router;
