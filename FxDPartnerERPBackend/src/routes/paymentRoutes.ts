import { Router } from 'express';
import multer from 'multer';
import {
  getPayments,
  getPayment,
  createPayment,
  updatePayment,
  deletePayment,
  getPaymentsByPartyId,
  getCustomerCreditExtensions,
  createCustomerCreditExtension,
  updateCustomerCreditExtension,
  getAllCreditExtensions,
  searchPayments,
  getPaymentStatistics
} from '../controllers/paymentController';

// Configure multer for handling FormData
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow images and PDFs
    if (file.mimetype.startsWith('image/') || file.mimetype === 'application/pdf') {
      cb(null, true);
    } else {
      cb(new Error('Only image and PDF files are allowed'));
    }
  }
});

const router = Router();

// Payment routes
router.get('/', getPayments);
router.get('/search', searchPayments);
router.get('/statistics', getPaymentStatistics);
router.get('/party/:partyId', getPaymentsByPartyId);
router.get('/customer/:customerId', getPaymentsByPartyId); // Alias for backward compatibility
router.get('/supplier/:supplierId', getPaymentsByPartyId); // Alias for backward compatibility
router.get('/:id', getPayment);
router.post('/', upload.any(), createPayment);
router.put('/:id', upload.any(), updatePayment);
router.delete('/:id', deletePayment);

// Customer credit extension routes
router.get('/credit-extensions/all', getAllCreditExtensions);
router.get('/credit-extensions/customer/:customerId', getCustomerCreditExtensions);
router.post('/credit-extensions', createCustomerCreditExtension);
router.put('/credit-extensions/:id', updateCustomerCreditExtension);

export default router;
