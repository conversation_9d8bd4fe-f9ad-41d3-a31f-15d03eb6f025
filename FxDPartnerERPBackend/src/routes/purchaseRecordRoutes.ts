import { Router } from 'express';
import {
  getPurchaseRecords,
  getPurchaseRecord,
  createPurchaseRecord,
  updatePurchaseRecord,
  deletePurchaseRecord,
  updatePurchaseRecordClosureStatus,
  getPurchaseRecordsBySupplierId,
  searchPurchaseRecords,
  getPurchaseRecordStatistics
} from '../controllers/purchaseRecordController';

const router = Router();

// Purchase record routes
router.get('/', getPurchaseRecords);
router.get('/search', searchPurchaseRecords);
router.get('/statistics', getPurchaseRecordStatistics);
router.get('/supplier/:supplierId', getPurchaseRecordsBySupplierId);
router.get('/:id', getPurchaseRecord);
router.post('/', createPurchaseRecord);
router.put('/:id', updatePurchaseRecord);
router.delete('/:id', deletePurchaseRecord);

// Purchase record closure management
router.patch('/:id/closure-status', updatePurchaseRecordClosureStatus);
router.put('/:id/closure-status', updatePurchaseRecordClosureStatus);

export default router;
