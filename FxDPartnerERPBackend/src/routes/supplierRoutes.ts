import { Router } from 'express';
import {
  getSuppliers,
  getSupplier,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  updateSupplierBalance,
  getPurchaseRecordsBySupplierId,
  getPaymentsBySupplierId,
  getSuppliersWithStats,
  searchSuppliers
} from '../controllers/supplierController';

const router = Router();

// Supplier routes
router.get('/', getSuppliers);
router.get('/search', searchSuppliers);
router.get('/stats', getSuppliersWithStats);
router.get('/:id', getSupplier);
router.post('/', createSupplier);
router.put('/:id', updateSupplier);
router.delete('/:id', deleteSupplier);

// Supplier balance management
router.put('/:id/balance', updateSupplierBalance);
router.patch('/:id/balance', updateSupplierBalance);

// Supplier related data
router.get('/:id/purchase-records', getPurchaseRecordsBySupplierId);
router.get('/:id/payments', getPaymentsBySupplierId);

export default router;
