import { Router } from 'express';
import {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  updateCustomerBalance,
  getSalesOrdersByCustomerId,
  getPaymentsByCustomerId,
  getCustomersWithStats,
  searchCustomers
} from '../controllers/customerController';

const router = Router();

// Customer routes
router.get('/', getCustomers);
router.get('/search', searchCustomers);
router.get('/stats', getCustomersWithStats);
router.get('/:id', getCustomer);
router.post('/', createCustomer);
router.put('/:id', updateCustomer);
router.delete('/:id', deleteCustomer);

// Customer balance management
router.patch('/:id/balance', updateCustomerBalance);

// Customer related data
router.get('/:id/sales-orders', getSalesOrdersByCustomerId);
router.get('/:id/payments', getPaymentsByCustomerId);

export default router;
