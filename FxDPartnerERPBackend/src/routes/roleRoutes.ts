import { Router } from 'express';
import {
  getRoles,
  getSystemRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  getRoleUsers,
  assignRoleToUser,
  removeRoleFromUser,
  getAvailablePermissions
} from '../controllers/roleController';
import { authenticateToken } from '../middleware/auth';
import { loadUserPermissions, requirePermission, requireAnyRole } from '../middleware/permissions';
import { PERMISSIONS } from '../models/Role';

const router = Router();

// Apply authentication and permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPermissions);

/**
 * @route GET /api/roles
 * @desc Get all roles for the organization
 * @access Private - Requires ROLE_READ permission
 */
router.get('/', requirePermission(PERMISSIONS.ROLE_READ), getRoles);

/**
 * @route GET /api/roles/system
 * @desc Get system roles (organization-independent)
 * @access Private - Requires admin role
 */
router.get('/system', requireAnyRole(['admin']), getSystemRoles);

/**
 * @route GET /api/roles/permissions
 * @desc Get available permissions
 * @access Private - Requires ROLE_READ permission
 */
router.get('/permissions', requirePermission(PERMISSIONS.ROLE_READ), getAvailablePermissions);

/**
 * @route GET /api/roles/:id
 * @desc Get role by ID
 * @access Private - Requires ROLE_READ permission
 */
router.get('/:id', requirePermission(PERMISSIONS.ROLE_READ), getRoleById);

/**
 * @route POST /api/roles
 * @desc Create new role
 * @access Private - Requires ROLE_CREATE permission
 */
router.post('/', requirePermission(PERMISSIONS.ROLE_CREATE), createRole);

/**
 * @route PUT /api/roles/:id
 * @desc Update role
 * @access Private - Requires ROLE_UPDATE permission
 */
router.put('/:id', requirePermission(PERMISSIONS.ROLE_UPDATE), updateRole);

/**
 * @route DELETE /api/roles/:id
 * @desc Delete role
 * @access Private - Requires ROLE_DELETE permission
 */
router.delete('/:id', requirePermission(PERMISSIONS.ROLE_DELETE), deleteRole);

/**
 * @route GET /api/roles/:id/users
 * @desc Get users assigned to a role
 * @access Private - Requires ROLE_READ permission
 */
router.get('/:id/users', requirePermission(PERMISSIONS.ROLE_READ), getRoleUsers);

/**
 * @route POST /api/roles/:id/assign
 * @desc Assign role to user
 * @access Private - Requires ROLE_UPDATE permission
 */
router.post('/:id/assign', requirePermission(PERMISSIONS.ROLE_UPDATE), assignRoleToUser);

/**
 * @route POST /api/roles/:id/remove
 * @desc Remove role from user
 * @access Private - Requires ROLE_UPDATE permission
 */
router.post('/:id/remove', requirePermission(PERMISSIONS.ROLE_UPDATE), removeRoleFromUser);

export default router;
