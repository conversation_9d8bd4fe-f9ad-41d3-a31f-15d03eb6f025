import { Router } from 'express';
import {
  getAllInventory,
  getAvailableInventory,
  getInventoryItem,
  createInventoryItem,
  updateInventoryItem,
  adjustInventory,
  adjustInventoryAsAnotherSKU,
  deleteInventoryItem,
  searchInventory,
  checkInventoryForSalesOrder
} from '../controllers/inventoryController';

const router = Router();

// Inventory routes
router.get('/', getAllInventory);
router.get('/available', getAvailableInventory);
router.get('/search', searchInventory);
router.post('/check-for-sales-order', checkInventoryForSalesOrder);
router.get('/:id', getInventoryItem);
router.post('/', createInventoryItem);
router.put('/:id', updateInventoryItem);
router.delete('/:id', deleteInventoryItem);

// Inventory adjustment routes
router.post('/adjust', adjustInventory);
router.post('/adjust-as-another-sku', adjustInventoryAsAnotherSKU);

export default router;
