import { Router } from 'express';
import {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductSKUs,
  createSKU,
  updateSKU,
  deleteSKU,
  getAllProductsAndSKUs,
  searchProducts
} from '../controllers/productController';

const router = Router();

// Product routes
router.get('/', getProducts);
router.get('/search', searchProducts);
router.get('/all-with-skus', getAllProductsAndSKUs);
router.get('/:id', getProduct);
router.post('/', createProduct);
router.put('/:id', updateProduct);
router.delete('/:id', deleteProduct);

// SKU routes
router.get('/:productId/skus', getProductSKUs);
router.post('/skus', createSKU);
router.put('/skus/:id', updateSKU);
router.delete('/skus/:id', deleteSKU);

export default router;
