import { Router } from 'express';
import {
  getProducts,
  getProduct,
  createProduct,
  updateProduct,
  deleteProduct,
  getProductSKUs,
  createSKU,
  updateSKU,
  deleteSKU,
  getAllProductsAndSKUs,
  searchProducts,
  searchProductsWithSKUs,
  getRecentProducts
} from '../controllers/productController';
import { authenticateToken } from '../middleware/auth';
import { 
  loadUserPagePermissions, 
  requirePageView, 
  requirePageEdit
} from '../middleware/permissions';

const router = Router();

// Apply authentication and page-based permission loading to all routes
router.use(authenticateToken);
router.use(loadUserPagePermissions);

// Page-based permission routes for products
// Page name: 'settings'

// Product routes
router.get('/', requirePageView('settings'), getProducts);
router.get('/search', requirePageView('settings'), searchProducts);
router.get('/search-with-skus', requirePageView('settings'), searchProductsWithSKUs);
router.get('/recent', requirePageView('settings'), getRecentProducts);
router.get('/all-with-skus', requirePageView('settings'), getAllProductsAndSKUs);
router.get('/:id', requirePageView('settings'), getProduct);
router.post('/', requirePageEdit('settings'), createProduct);
router.put('/:id', requirePageEdit('settings'), updateProduct);
router.delete('/:id', requirePageEdit('settings'), deleteProduct);

// SKU routes
router.get('/:productId/skus', requirePageView('settings'), getProductSKUs);
router.post('/skus', requirePageEdit('settings'), createSKU);
router.put('/skus/:id', requirePageEdit('settings'), updateSKU);
router.delete('/skus/:id', requirePageEdit('settings'), deleteSKU);

export default router;
