import express from 'express';
const router = express.Router();
import {
  getVehicleArrivals,
  getVehicleArrival,
  createVehicleArrival,
  updateVehicleArrival,
  updateVehicleArrivalStatus,
  deleteVehicleArrival
} from '../controllers/vehicleArrivalController';

// GET /api/procurement/vehicle-arrivals - Get all vehicle arrivals
router.get('/', getVehicleArrivals);

// GET /api/procurement/vehicle-arrivals/:id - Get single vehicle arrival
router.get('/:id', getVehicleArrival);

// POST /api/procurement/vehicle-arrivals - Create new vehicle arrival
router.post('/', createVehicleArrival);

// PUT /api/procurement/vehicle-arrivals/:id - Update vehicle arrival
router.put('/:id', updateVehicleArrival);

// PATCH /api/procurement/vehicle-arrivals/:id/status - Update vehicle arrival status
router.patch('/:id/status', updateVehicleArrivalStatus);

// DELETE /api/procurement/vehicle-arrivals/:id - Delete vehicle arrival
router.delete('/:id', deleteVehicleArrival);

export default router;
