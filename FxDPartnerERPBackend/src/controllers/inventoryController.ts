import { Request, Response } from 'express';
import { Transaction, Op } from 'sequelize';
import sequelize from '../config/sequelize';
import { CurrentInventory, CreateInventoryData, UpdateInventoryData, InventoryAdjustmentData } from '../models/Inventory';
import { Product, SKU } from '../models/Product';
import { Organization } from '../models/Organization';
import { InventoryTransaction } from '../models/InventoryTransaction';
import { InventoryDump } from '../models/InventoryDump';
import { VehicleArrival } from '../models/VehicleArrival';
import { SalesOrder } from '../models/SalesOrder';
import { PurchaseRecord } from '../models/PurchaseRecord';
import { User } from '../models/User';

// Get all inventory
export const getAllInventory = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const inventory = await CurrentInventory.findAll({
      where: { organization_id: organizationId },
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ],
      order: [['product_name', 'ASC']]
    });

    res.json(inventory);
  } catch (error) {
    console.error('Error fetching inventory:', error);
    res.status(500).json({ error: 'Failed to fetch inventory' });
  }
};

// Get available inventory (positive quantities only)
export const getAvailableInventory = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const inventory = await CurrentInventory.findAll({
      where: { 
        organization_id: organizationId
        // Note: Removed available_quantity > 0 filter to show all inventory including negative
      },
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ],
      order: [['product_name', 'ASC']]
    });

    res.json(inventory);
  } catch (error) {
    console.error('Error fetching available inventory:', error);
    res.status(500).json({ error: 'Failed to fetch available inventory' });
  }
};

// Get single inventory item
export const getInventoryItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const inventoryItem = await CurrentInventory.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ]
    });

    if (!inventoryItem) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    res.json(inventoryItem);
  } catch (error) {
    console.error('Error fetching inventory item:', error);
    res.status(500).json({ error: 'Failed to fetch inventory item' });
  }
};

// Create inventory item
export const createInventoryItem = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const inventoryData: CreateInventoryData = {
      ...req.body,
      organization_id: organizationId
    };

    // Validate required fields
    if (!inventoryData.product_id || !inventoryData.sku_id || !inventoryData.product_name || !inventoryData.sku_code) {
      return res.status(400).json({ 
        error: 'Product ID, SKU ID, product name, and SKU code are required' 
      });
    }

    // Validate product and SKU exist
    const product = await Product.findOne({
      where: { 
        id: inventoryData.product_id,
        organization_id: organizationId 
      }
    });

    if (!product) {
      return res.status(400).json({ error: 'Invalid product ID' });
    }

    const sku = await SKU.findOne({
      where: { 
        id: inventoryData.sku_id,
        product_id: inventoryData.product_id
      }
    });

    if (!sku) {
      return res.status(400).json({ error: 'Invalid SKU ID' });
    }

    // Check if inventory item already exists
    const existingItem = await CurrentInventory.findOne({
      where: {
        organization_id: organizationId,
        product_id: inventoryData.product_id,
        sku_id: inventoryData.sku_id
      }
    });

    if (existingItem) {
      // Update existing item
      await existingItem.update({
        available_quantity: existingItem.available_quantity + inventoryData.available_quantity,
        total_weight: existingItem.total_weight + inventoryData.total_weight,
        last_updated_at: new Date()
      });

      const updatedItem = await CurrentInventory.findByPk(existingItem.id, {
        include: [
          {
            model: Product,
            attributes: ['id', 'name']
          },
          {
            model: SKU,
            attributes: ['id', 'code', 'unit_type', 'unit_weight']
          }
        ]
      });

      return res.json(updatedItem);
    }

    // Create new inventory item
    const inventoryItem = await CurrentInventory.create({
      ...inventoryData,
      last_updated_at: new Date()
    } as any);

    // Fetch the created item with associations
    const createdItem = await CurrentInventory.findByPk(inventoryItem.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ]
    });

    res.status(201).json(createdItem);
  } catch (error) {
    console.error('Error creating inventory item:', error);
    res.status(500).json({ error: 'Failed to create inventory item' });
  }
};

// Update inventory item
export const updateInventoryItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const updateData: UpdateInventoryData = {
      ...req.body,
      last_updated_at: new Date()
    };

    // Find inventory item
    const inventoryItem = await CurrentInventory.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!inventoryItem) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    // Update inventory item
    await inventoryItem.update(updateData);

    // Fetch the updated item with associations
    const updatedItem = await CurrentInventory.findByPk(id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ]
    });

    res.json(updatedItem);
  } catch (error) {
    console.error('Error updating inventory item:', error);
    res.status(500).json({ error: 'Failed to update inventory item' });
  }
};

// Adjust inventory
export const adjustInventory = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const adjustmentData: InventoryAdjustmentData = req.body;

    // Validate required fields
    if (!adjustmentData.product_id || !adjustmentData.sku_id || !adjustmentData.adjustment_type || !adjustmentData.reason) {
      return res.status(400).json({ 
        error: 'Product ID, SKU ID, adjustment type, and reason are required' 
      });
    }

    // Find inventory item
    const inventoryItem = await CurrentInventory.findOne({
      where: {
        organization_id: organizationId,
        product_id: adjustmentData.product_id,
        sku_id: adjustmentData.sku_id
      },
      transaction
    });

    if (!inventoryItem) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    let newQuantity = inventoryItem.available_quantity;
    let newWeight = inventoryItem.total_weight;

    // Apply adjustment
    switch (adjustmentData.adjustment_type) {
      case 'add':
        newQuantity += adjustmentData.quantity_change;
        newWeight += adjustmentData.weight_change || 0;
        break;
      case 'subtract':
        newQuantity -= adjustmentData.quantity_change;
        newWeight -= adjustmentData.weight_change || 0;
        break;
      case 'set':
        newQuantity = adjustmentData.quantity_change;
        newWeight = adjustmentData.weight_change || newWeight;
        break;
      default:
        return res.status(400).json({ error: 'Invalid adjustment type' });
    }

    // Update inventory item
    await inventoryItem.update({
      available_quantity: newQuantity,
      total_weight: Math.max(0, newWeight), // Ensure weight doesn't go negative
      last_updated_at: new Date()
    }, { transaction });

    await transaction.commit();

    // Fetch the updated item with associations
    const updatedItem = await CurrentInventory.findByPk(inventoryItem.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ]
    });

    res.json({
      message: 'Inventory adjusted successfully',
      item: updatedItem,
      adjustment: {
        type: adjustmentData.adjustment_type,
        quantity_change: adjustmentData.quantity_change,
        weight_change: adjustmentData.weight_change,
        reason: adjustmentData.reason,
        notes: adjustmentData.notes
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error adjusting inventory:', error);
    res.status(500).json({ error: 'Failed to adjust inventory' });
  }
};

// Mark inventory as another SKU
export const adjustInventoryAsAnotherSKU = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const {
      currentProductId,
      currentSkuId,
      newProductId,
      newSkuId,
      reason
    } = req.body;

    // Validate required fields
    if (!currentProductId || !currentSkuId || !newProductId || !newSkuId || !reason) {
      return res.status(400).json({ 
        error: 'Current product ID, current SKU ID, new product ID, new SKU ID, and reason are required' 
      });
    }

    // Find current inventory item
    const currentItem = await CurrentInventory.findOne({
      where: {
        organization_id: organizationId,
        product_id: currentProductId,
        sku_id: currentSkuId
      },
      transaction
    });

    if (!currentItem) {
      return res.status(404).json({ error: 'Current inventory item not found' });
    }

    // Get new product and SKU details
    const newProduct = await Product.findOne({
      where: { 
        id: newProductId,
        organization_id: organizationId 
      },
      transaction
    });

    if (!newProduct) {
      return res.status(400).json({ error: 'New product not found' });
    }

    const newSku = await SKU.findOne({
      where: { 
        id: newSkuId,
        product_id: newProductId
      },
      transaction
    });

    if (!newSku) {
      return res.status(400).json({ error: 'New SKU not found' });
    }

    // Check if new inventory item already exists
    const existingNewItem = await CurrentInventory.findOne({
      where: {
        organization_id: organizationId,
        product_id: newProductId,
        sku_id: newSkuId
      },
      transaction
    });

    if (existingNewItem) {
      // Update existing item with additional quantity
      await existingNewItem.update({
        available_quantity: existingNewItem.available_quantity + currentItem.available_quantity,
        total_weight: existingNewItem.total_weight + currentItem.total_weight,
        last_updated_at: new Date()
      }, { transaction });
    } else {
      // Create new inventory item
      await CurrentInventory.create({
        organization_id: organizationId,
        product_id: newProductId,
        sku_id: newSkuId,
        product_name: newProduct.name,
        sku_code: newSku.code,
        unit_type: newSku.unit_type,
        available_quantity: currentItem.available_quantity,
        total_weight: currentItem.total_weight,
        last_updated_at: new Date()
      }, { transaction });
    }

    // Delete the original inventory item
    await currentItem.destroy({ transaction });

    await transaction.commit();

    res.json({ 
      message: 'Inventory successfully adjusted as another SKU',
      reason
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error adjusting inventory as another SKU:', error);
    res.status(500).json({ error: 'Failed to adjust inventory as another SKU' });
  }
};

// Delete inventory item
export const deleteInventoryItem = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find inventory item
    const inventoryItem = await CurrentInventory.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!inventoryItem) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    await inventoryItem.destroy();

    res.json({ message: 'Inventory item deleted successfully' });
  } catch (error) {
    console.error('Error deleting inventory item:', error);
    res.status(500).json({ error: 'Failed to delete inventory item' });
  }
};

// Search inventory
export const searchInventory = async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const inventory = await CurrentInventory.findAll({
      where: {
        organization_id: organizationId,
        [Op.or]: [
          { product_name: { [Op.like]: `%${q}%` } },
          { sku_code: { [Op.like]: `%${q}%` } },
        ]
      },
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ],
      limit: 20,
      order: [['product_name', 'ASC']]
    });

    res.json(inventory);
  } catch (error) {
    console.error('Error searching inventory:', error);
    res.status(500).json({ error: 'Failed to search inventory' });
  }
};

// Get inventory history for a product/SKU
export const getInventoryHistory = async (req: Request, res: Response) => {
  try {
    const { productId, skuId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Get all inventory transactions for this product/SKU
    const transactions = await InventoryTransaction.findAll({
      where: {
        organization_id: organizationId,
        product_id: productId,
        sku_id: skuId
      },
      include: [
        {
          model: Product,
          attributes: ['name']
        },
        {
          model: SKU,
          attributes: ['sku_code', 'unit_type']
        },
        {
          model: User,
          as: 'performer',
          attributes: ['first_name', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Transform transactions into timeline format
    const timeline = [];
    let runningBalance = 0;

    // Calculate running balance by processing transactions in chronological order
    const chronologicalTransactions = [...transactions].reverse();
    
    for (const transaction of chronologicalTransactions) {
      runningBalance += transaction.quantity_change;
      
      let transactionType = 'Adjustment';
      let icon = '±';
      let color = 'blue';
      let description = transaction.reason;
      let reference = '';

      // Determine transaction type and formatting
      switch (transaction.transaction_type) {
        case 'return_to_inventory':
          transactionType = 'Return';
          icon = '+';
          color = 'green';
          description = 'Items returned to inventory';
          reference = `GRN Return`;
          break;
        case 'sale':
          transactionType = 'Sale';
          icon = '-';
          color = 'red';
          description = 'Items sold to customer';
          reference = `Order: ${transaction.reference_id}`;
          break;
        case 'purchase':
          transactionType = 'Arrival';
          icon = '+';
          color = 'green';
          description = 'Items received from supplier';
          reference = `Purchase Record: ${transaction.reference_id}`;
          break;
        case 'vehicle_arrival':
          transactionType = 'Arrival';
          icon = '+';
          color = 'green';
          description = 'Items received from vehicle';
          reference = `Vehicle Arrival: ${transaction.reference_id}`;
          break;
        case 'adjustment':
          transactionType = 'Adjustment';
          icon = transaction.quantity_change > 0 ? '+' : '-';
          color = transaction.quantity_change > 0 ? 'green' : 'red';
          description = transaction.reason;
          reference = 'Manual Adjustment';
          break;
      }

      timeline.push({
        id: transaction.id,
        type: transactionType,
        icon,
        color,
        quantity_change: transaction.quantity_change,
        running_balance: runningBalance,
        description,
        reference,
        reference_type: transaction.reference_type,
        reference_id: transaction.reference_id,
        performed_by: transaction.performer?.first_name || 'System',
        notes: transaction.notes,
        created_at: transaction.created_at
      });
    }

    // Reverse to show most recent first
    timeline.reverse();

    res.json({
      product_id: productId,
      sku_id: skuId,
      current_balance: runningBalance,
      timeline
    });
  } catch (error) {
    console.error('Error fetching inventory history:', error);
    res.status(500).json({ error: 'Failed to fetch inventory history' });
  }
};

// Check inventory for sales order (negative inventory warnings)
export const checkInventoryForSalesOrder = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { items } = req.body;

    if (!items || !Array.isArray(items)) {
      return res.status(400).json({ error: 'Items array is required' });
    }

    const warnings = [];

    for (const item of items) {
      const productId = item.product_id || item.productId;
      const skuId = item.sku_id || item.skuId;
      const quantity = item.quantity || 0;

      if (!productId || !skuId || quantity <= 0) {
        continue; // Skip invalid items
      }

      // Get current inventory
      const currentInventory = await CurrentInventory.findOne({
        where: {
          organization_id: organizationId,
          product_id: productId,
          sku_id: skuId
        }
      });

      if (!currentInventory) {
        warnings.push({
          productId,
          skuId,
          productName: item.product_name || item.productName || 'Unknown Product',
          skuCode: item.sku_code || item.skuCode || 'Unknown SKU',
          currentQuantity: 0,
          requestedQuantity: quantity,
          resultingQuantity: -quantity,
          type: 'not_found'
        });
        continue;
      }

      const currentQuantity = currentInventory.available_quantity || 0;
      const resultingQuantity = currentQuantity - quantity;

      if (resultingQuantity < 0) {
        warnings.push({
          productId,
          skuId,
          productName: currentInventory.product_name,
          skuCode: currentInventory.sku_code,
          currentQuantity,
          requestedQuantity: quantity,
          resultingQuantity,
          type: 'negative'
        });
      }
    }

    res.json(warnings);
  } catch (error) {
    console.error('Error checking inventory for sales order:', error);
    res.status(500).json({ error: 'Failed to check inventory' });
  }
};

// Mark inventory as dump
export const markInventoryAsDump = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const userId = (req as any).user?.id;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const {
      product_id,
      sku_id,
      quantity,
      weight,
      reason,
      reason_notes
    } = req.body;

    // Validate required fields
    if (!product_id || !sku_id || !quantity || !reason) {
      return res.status(400).json({ 
        error: 'Product ID, SKU ID, quantity, and reason are required' 
      });
    }

    // Find inventory item
    const inventoryItem = await CurrentInventory.findOne({
      where: {
        organization_id: organizationId,
        product_id,
        sku_id
      },
      transaction
    });

    if (!inventoryItem) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    // Check if sufficient quantity is available
    if (inventoryItem.available_quantity < quantity) {
      return res.status(400).json({ 
        error: `Insufficient quantity. Available: ${inventoryItem.available_quantity}, Requested: ${quantity}` 
      });
    }

    // Calculate weight if not provided
    const dumpWeight = weight || 0;

    // Create dump record
    const dumpRecord = await InventoryDump.create({
      organization_id: organizationId,
      product_id,
      sku_id,
      product_name: inventoryItem.product_name,
      sku_code: inventoryItem.sku_code,
      dumped_quantity: quantity,
      dumped_weight: dumpWeight,
      reason,
      reason_notes,
      dumped_by: userId,
      dumped_at: new Date()
    }, { transaction });

    // Update inventory quantity
    await inventoryItem.update({
      available_quantity: inventoryItem.available_quantity - quantity,
      total_weight: Math.max(0, inventoryItem.total_weight - dumpWeight),
      last_updated_at: new Date()
    }, { transaction });

    // Create inventory transaction record
    await InventoryTransaction.create({
      organization_id: organizationId,
      product_id,
      sku_id,
      transaction_type: 'adjustment',
      quantity_change: -quantity,
      weight_change: -dumpWeight,
      reason: `Marked as dump: ${reason}`,
      notes: reason_notes,
      reference_type: 'manual_adjustment',
      reference_id: dumpRecord.id,
      performed_by: userId
    }, { transaction });

    await transaction.commit();

    // Fetch updated inventory item
    const updatedItem = await CurrentInventory.findByPk(inventoryItem.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ]
    });

    res.json({
      message: 'Inventory marked as dump successfully',
      dump_record: dumpRecord,
      updated_inventory: updatedItem
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error marking inventory as dump:', error);
    res.status(500).json({ error: 'Failed to mark inventory as dump' });
  }
};

// Update SKU details
export const updateSKUDetails = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const userId = (req as any).user?.id;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const {
      current_product_id,
      current_sku_id,
      new_sku_code,
      new_unit_type,
      new_unit_weight,
      reason
    } = req.body;

    // Validate required fields
    if (!current_product_id || !current_sku_id || !reason) {
      return res.status(400).json({ 
        error: 'Current product ID, current SKU ID, and reason are required' 
      });
    }

    // Find current inventory item
    const currentInventory = await CurrentInventory.findOne({
      where: {
        organization_id: organizationId,
        product_id: current_product_id,
        sku_id: current_sku_id
      },
      transaction
    });

    if (!currentInventory) {
      return res.status(404).json({ error: 'Current inventory item not found' });
    }

    // Find current SKU
    const currentSku = await SKU.findOne({
      where: {
        id: current_sku_id,
        product_id: current_product_id
      },
      transaction
    });

    if (!currentSku) {
      return res.status(404).json({ error: 'Current SKU not found' });
    }

    // Check if new SKU code already exists (if provided)
    if (new_sku_code && new_sku_code !== currentSku.code) {
      const existingSku = await SKU.findOne({
        where: {
          code: new_sku_code,
          product_id: current_product_id
        },
        transaction
      });

      if (existingSku) {
        return res.status(400).json({ error: 'SKU code already exists for this product' });
      }
    }

    // Update SKU details
    const updateData: any = {};
    if (new_sku_code) updateData.code = new_sku_code;
    if (new_unit_type) updateData.unit_type = new_unit_type;
    if (new_unit_weight !== undefined) updateData.unit_weight = new_unit_weight;

    if (Object.keys(updateData).length > 0) {
      await currentSku.update(updateData, { transaction });
    }

    // Update inventory record with new SKU details
    const inventoryUpdateData: any = {};
    if (new_sku_code) inventoryUpdateData.sku_code = new_sku_code;
    if (new_unit_type) inventoryUpdateData.unit_type = new_unit_type;
    inventoryUpdateData.last_updated_at = new Date();

    if (Object.keys(inventoryUpdateData).length > 0) {
      await currentInventory.update(inventoryUpdateData, { transaction });
    }

    // Create inventory transaction record
    await InventoryTransaction.create({
      organization_id: organizationId,
      product_id: current_product_id,
      sku_id: current_sku_id,
      transaction_type: 'adjustment',
      quantity_change: 0,
      weight_change: 0,
      reason: `SKU updated: ${reason}`,
      notes: `Updated fields: ${Object.keys(updateData).join(', ')}`,
      reference_type: 'manual_adjustment',
      reference_id: current_sku_id,
      performed_by: userId
    }, { transaction });

    await transaction.commit();

    // Fetch updated inventory item
    const updatedItem = await CurrentInventory.findByPk(currentInventory.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ]
    });

    res.json({
      message: 'SKU details updated successfully',
      updated_inventory: updatedItem,
      changes: updateData
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating SKU details:', error);
    res.status(500).json({ error: 'Failed to update SKU details' });
  }
};

// Manual inventory adjustment
export const manualInventoryAdjustment = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const userId = (req as any).user?.id;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const {
      product_id,
      sku_id,
      adjustment_type,
      quantity_change,
      weight_change,
      reason,
      notes
    } = req.body;

    // Validate required fields
    if (!product_id || !sku_id || !adjustment_type || !quantity_change || !reason) {
      return res.status(400).json({ 
        error: 'Product ID, SKU ID, adjustment type, quantity change, and reason are required' 
      });
    }

    // Validate adjustment type
    if (!['add', 'subtract', 'set'].includes(adjustment_type)) {
      return res.status(400).json({ error: 'Invalid adjustment type. Must be add, subtract, or set' });
    }

    // Find inventory item
    const inventoryItem = await CurrentInventory.findOne({
      where: {
        organization_id: organizationId,
        product_id,
        sku_id
      },
      transaction
    });

    if (!inventoryItem) {
      return res.status(404).json({ error: 'Inventory item not found' });
    }

    let newQuantity = inventoryItem.available_quantity;
    let newWeight = inventoryItem.total_weight;
    let actualQuantityChange = 0;
    let actualWeightChange = 0;

    // Apply adjustment
    switch (adjustment_type) {
      case 'add':
        newQuantity += quantity_change;
        newWeight += weight_change || 0;
        actualQuantityChange = quantity_change;
        actualWeightChange = weight_change || 0;
        break;
      case 'subtract':
        newQuantity -= quantity_change;
        newWeight -= weight_change || 0;
        actualQuantityChange = -quantity_change;
        actualWeightChange = -(weight_change || 0);
        break;
      case 'set':
        actualQuantityChange = quantity_change - inventoryItem.available_quantity;
        actualWeightChange = (weight_change || inventoryItem.total_weight) - inventoryItem.total_weight;
        newQuantity = quantity_change;
        newWeight = weight_change !== undefined ? weight_change : inventoryItem.total_weight;
        break;
    }

    // Ensure weight doesn't go negative
    newWeight = Math.max(0, newWeight);

    // Update inventory item
    await inventoryItem.update({
      available_quantity: newQuantity,
      total_weight: newWeight,
      last_updated_at: new Date()
    }, { transaction });

    // Create inventory transaction record
    await InventoryTransaction.create({
      organization_id: organizationId,
      product_id,
      sku_id,
      transaction_type: 'adjustment',
      quantity_change: actualQuantityChange,
      weight_change: actualWeightChange,
      reason,
      notes,
      reference_type: 'manual_adjustment',
      reference_id: inventoryItem.id,
      performed_by: userId
    }, { transaction });

    await transaction.commit();

    // Fetch updated inventory item
    const updatedItem = await CurrentInventory.findByPk(inventoryItem.id, {
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        }
      ]
    });

    res.json({
      message: 'Manual inventory adjustment completed successfully',
      updated_inventory: updatedItem,
      adjustment: {
        type: adjustment_type,
        quantity_change: actualQuantityChange,
        weight_change: actualWeightChange,
        reason,
        notes
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error performing manual inventory adjustment:', error);
    res.status(500).json({ error: 'Failed to perform manual inventory adjustment' });
  }
};

// Get inventory dumps with filtering
export const getInventoryDumps = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { startDate, endDate, reason, productId, skuId } = req.query;

    // Build where clause
    const whereClause: any = { organization_id: organizationId };

    // Date filtering
    if (startDate || endDate) {
      whereClause.dumped_at = {};
      if (startDate) {
        whereClause.dumped_at[Op.gte] = new Date(startDate as string);
      }
      if (endDate) {
        const endDateTime = new Date(endDate as string);
        endDateTime.setHours(23, 59, 59, 999);
        whereClause.dumped_at[Op.lte] = endDateTime;
      }
    }

    // Reason filtering
    if (reason) {
      whereClause.reason = reason;
    }

    // Product/SKU filtering
    if (productId) {
      whereClause.product_id = productId;
    }
    if (skuId) {
      whereClause.sku_id = skuId;
    }

    const dumps = await InventoryDump.findAll({
      where: whereClause,
      include: [
        {
          model: Product,
          as: 'product',
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          as: 'sku',
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        },
        {
          model: User,
          as: 'dumper',
          attributes: ['id', 'first_name', 'email']
        }
      ],
      order: [['dumped_at', 'DESC']]
    });

    // Calculate summary statistics
    const summary = {
      total_dumps: dumps.length,
      total_quantity: dumps.reduce((sum, dump) => sum + dump.dumped_quantity, 0),
      total_weight: dumps.reduce((sum, dump) => sum + dump.dumped_weight, 0),
      by_reason: dumps.reduce((acc: any, dump) => {
        acc[dump.reason] = (acc[dump.reason] || 0) + 1;
        return acc;
      }, {})
    };

    res.json({
      dumps,
      summary
    });
  } catch (error) {
    console.error('Error fetching inventory dumps:', error);
    res.status(500).json({ error: 'Failed to fetch inventory dumps' });
  }
};

// Get inventory adjustment reports
export const getInventoryAdjustmentReports = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { startDate, endDate, type, productId, skuId } = req.query;

    // Build where clause for inventory transactions
    const whereClause: any = { 
      organization_id: organizationId,
      transaction_type: {
        [Op.in]: ['adjustment']
      }
    };

    // Date filtering
    if (startDate || endDate) {
      whereClause.created_at = {};
      if (startDate) {
        whereClause.created_at[Op.gte] = new Date(startDate as string);
      }
      if (endDate) {
        const endDateTime = new Date(endDate as string);
        endDateTime.setHours(23, 59, 59, 999);
        whereClause.created_at[Op.lte] = endDateTime;
      }
    }

    // Type filtering
    if (type && ['dump', 'manual_adjustment', 'sku_update'].includes(type as string)) {
      whereClause.transaction_type = type;
    }

    // Product/SKU filtering
    if (productId) {
      whereClause.product_id = productId;
    }
    if (skuId) {
      whereClause.sku_id = skuId;
    }

    const transactions = await InventoryTransaction.findAll({
      where: whereClause,
      include: [
        {
          model: Product,
          attributes: ['id', 'name']
        },
        {
          model: SKU,
          attributes: ['id', 'code', 'unit_type', 'unit_weight']
        },
        {
          model: User,
          as: 'performer',
          attributes: ['id', 'first_name', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Calculate summary statistics
    const summary = {
      total_adjustments: transactions.length,
      by_type: transactions.reduce((acc: any, txn) => {
        acc[txn.transaction_type] = (acc[txn.transaction_type] || 0) + 1;
        return acc;
      }, {}),
      quantity_changes: {
        positive: transactions.filter(txn => txn.quantity_change > 0).reduce((sum, txn) => sum + txn.quantity_change, 0),
        negative: transactions.filter(txn => txn.quantity_change < 0).reduce((sum, txn) => sum + Math.abs(txn.quantity_change), 0)
      }
    };

    res.json({
      adjustments: transactions,
      summary
    });
  } catch (error) {
    console.error('Error fetching inventory adjustment reports:', error);
    res.status(500).json({ error: 'Failed to fetch inventory adjustment reports' });
  }
};
