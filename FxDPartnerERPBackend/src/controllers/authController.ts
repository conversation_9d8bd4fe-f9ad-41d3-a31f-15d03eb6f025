import { Request, Response } from 'express';
import bcrypt from 'bcrypt';
import { User } from '../models/User';
import { Organization } from '../models/Organization';
import { UserOrganization } from '../models/UserOrganization';
import { UserRole } from '../models/UserRole';
import { Role } from '../models/Role';
import { generateToken } from '../middleware/auth';
import { getUserPermissions, getUserRoles, getUserPages } from '../middleware/permissions';

interface AuthenticatedRequest extends Request {
  user?: User;
  userId?: string;
  organizationId?: string;
}

export class AuthController {
  // User login
  static async login(req: Request, res: Response) {
    try {
      const { email, password } = req.body;

      // Validate input
      if (!email || !password) {
        return res.status(400).json({
          success: false,
          message: 'Email and password are required'
        });
      }

      // Find user by email
      const user = await User.findOne({
        where: { email: email.toLowerCase() },
        include: [
          {
            model: Organization,
            as: 'organization'
          }
        ]
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Check if user is active
      if (user.status !== 'active') {
        return res.status(401).json({
          success: false,
          message: 'Account is not active. Please contact administrator.'
        });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password_hash);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Get user's primary organization
      let primaryOrganization = user.organization;
      if (!primaryOrganization) {
        const userOrg = await UserOrganization.findOne({
          where: { 
            user_id: user.id,
            is_primary: true,
            status: 'active'
          },
          include: [Organization]
        });
        primaryOrganization = (userOrg?.organization as Organization) || null;
      }

      // Get user's primary role in the organization
      let primaryRole = null;
      if (primaryOrganization && primaryOrganization.id) {
        const userRole = await UserRole.findOne({
          where: { 
            user_id: user.id,
            organization_id: primaryOrganization.id,
            is_primary: true,
            status: 'active'
          },
          include: [Role]
        });
        primaryRole = userRole?.role || null;
      }

      // Generate JWT token
      const token = generateToken(user, primaryOrganization?.id);

      // Update last login
      await user.update({ last_login_at: new Date() });

      // Get user permissions, roles, and pages for the organization
      let permissions: string[] = [];
      let roles: string[] = [];
      let pages: string[] = [];
      
      if (primaryOrganization?.id) {
        [permissions, roles, pages] = await Promise.all([
          getUserPermissions(user.id, primaryOrganization.id),
          getUserRoles(user.id, primaryOrganization.id),
          getUserPages(user.id, primaryOrganization.id)
        ]);
      }

      // Prepare user data (without password)
      const userData = {
        id: user.id,
        email: user.email,
        name: user.first_name,
        first_name: user.first_name,
        phone: user.phone,
        status: user.status,
        organization_id: primaryOrganization?.id,
        organization: primaryOrganization ? {
          id: primaryOrganization.id,
          name: primaryOrganization.name,
          code: primaryOrganization.code,
          status: primaryOrganization.status
        } : null,
        role: primaryRole ? primaryRole.name : null,
        roles,
        permissions,
        pages,
        last_login_at: user.last_login_at,
        created_at: user.created_at
      };

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          token,
          user: userData
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during login'
      });
    }
  }

  // User logout
  static async logout(req: AuthenticatedRequest, res: Response) {
    try {
      // In a real implementation, you might want to blacklist the token
      // For now, we'll just return success
      res.json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during logout'
      });
    }
  }

  // Verify token
  static async verify(req: AuthenticatedRequest, res: Response) {
    try {
      // If we reach here, the authenticateToken middleware has already validated the token
      const user = req.user;
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'User not found'
        });
      }

      // Get user's primary organization and role
      let primaryOrganization = user.organization;
      if (!primaryOrganization) {
        const userOrg = await UserOrganization.findOne({
          where: { 
            user_id: user.id,
            is_primary: true,
            status: 'active'
          },
          include: [Organization]
        });
        primaryOrganization = (userOrg?.organization as Organization) || null;
      }

      let primaryRole = null;
      if (primaryOrganization && primaryOrganization.id) {
        const userRole = await UserRole.findOne({
          where: { 
            user_id: user.id,
            organization_id: primaryOrganization.id,
            is_primary: true,
            status: 'active'
          },
          include: [Role]
        });
        primaryRole = userRole?.role;
      }

      const userData = {
        id: user.id,
        email: user.email,
        name: user.first_name,
        first_name: user.first_name,
        phone: user.phone,
        status: user.status,
        organization_id: primaryOrganization?.id,
        organization: primaryOrganization ? {
          id: primaryOrganization.id,
          name: primaryOrganization.name,
          code: primaryOrganization.code,
          status: primaryOrganization.status
        } : null,
        role: primaryRole ? primaryRole.name : null,
        last_login_at: user.last_login_at,
        created_at: user.created_at
      };

      res.json({
        success: true,
        message: 'Token is valid',
        data: {
          user: userData
        }
      });
    } catch (error) {
      console.error('Token verification error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during token verification'
      });
    }
  }

  // Switch organization
  static async switchOrganization(req: AuthenticatedRequest, res: Response) {
    try {
      const { organizationId } = req.params;
      const user = req.user;

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'User not found'
        });
      }

      if (!organizationId) {
        return res.status(400).json({
          success: false,
          message: 'Organization ID is required'
        });
      }

      // Verify user has access to this organization
      const userOrganization = await UserOrganization.findOne({
        where: {
          user_id: user.id,
          organization_id: organizationId,
          status: 'active'
        },
        include: [Organization]
      });

      if (!userOrganization) {
        return res.status(403).json({
          success: false,
          message: 'Access denied to this organization'
        });
      }

      const organization = userOrganization.organization;

      // Get user's role in this organization
      const userRole = await UserRole.findOne({
        where: {
          user_id: user.id,
          organization_id: organizationId,
          status: 'active'
        },
        include: [Role]
      });

      // Generate new token with updated organization context
      const token = generateToken(user, organizationId);

      // Get user permissions, roles, and pages for the new organization
      const [permissions, roles, pages] = await Promise.all([
        getUserPermissions(user.id, organizationId),
        getUserRoles(user.id, organizationId),
        getUserPages(user.id, organizationId)
      ]);

      // Prepare updated user data
      const userData = {
        id: user.id,
        email: user.email,
        name: user.first_name,
        first_name: user.first_name,
        phone: user.phone,
        status: user.status,
        organization_id: organizationId,
        organization: {
          id: organization.id,
          name: organization.name,
          code: organization.code,
          status: organization.status
        },
        role: userRole?.role?.name || null,
        roles,
        permissions,
        pages,
        last_login_at: user.last_login_at,
        created_at: user.created_at
      };

      res.json({
        success: true,
        message: 'Organization switched successfully',
        data: {
          token,
          user: userData
        }
      });
    } catch (error) {
      console.error('Switch organization error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while switching organization'
      });
    }
  }

  // Get current user
  static async getCurrentUser(req: AuthenticatedRequest, res: Response) {
    try {
      const user = req.user;
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'User not found'
        });
      }

      // Get all user organizations and roles
      const userOrganizations = await UserOrganization.findAll({
        where: { 
          user_id: user.id,
          status: 'active'
        },
        include: [Organization]
      });

      const userRoles = await UserRole.findAll({
        where: { 
          user_id: user.id,
          status: 'active'
        },
        include: [Role, Organization]
      });

      const userData = {
        id: user.id,
        email: user.email,
        name: user.first_name,
        first_name: user.first_name,
        phone: user.phone,
        status: user.status,
        organization_id: user.organization_id,
        organization: user.organization,
        organizations: userOrganizations.map(uo => ({
          id: uo.organization.id,
          name: uo.organization.name,
          code: uo.organization.code,
          status: uo.organization.status,
          is_primary: uo.is_primary,
          user_status: uo.status
        })),
        roles: userRoles.map(ur => ({
          id: ur.role.id,
          name: ur.role.name,
          description: ur.role.description,
          organization_id: ur.organization_id,
          organization_name: ur.organization?.name,
          is_primary: ur.is_primary,
          status: ur.status
        })),
        last_login_at: user.last_login_at,
        created_at: user.created_at
      };

      res.json({
        success: true,
        data: {
          user: userData
        }
      });
    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching user data'
      });
    }
  }
}
