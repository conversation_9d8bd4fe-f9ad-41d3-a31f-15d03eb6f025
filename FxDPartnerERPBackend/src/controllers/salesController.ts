import { Request, Response } from 'express';
import { Transaction, Op } from 'sequelize';
import sequelize from '../config/sequelize';
import { SalesOrder, SalesOrderItem, SalesOrderPayment } from '../models/SalesOrder';
import { SalesOrderDiscount } from '../models/SalesOrderDiscount';
import { Customer } from '../models/Customer';
import { CurrentInventory } from '../models/Inventory';
import { Product, SKU } from '../models/Product';
import { InventoryTransactionService } from '../services/inventoryTransactionService';
import { GRNReturnPDDRequest, GRNReturnPDDItem, NegativeInventory } from '../models';

// Helper function to create product and SKU if they don't exist
const createProductAndSKUIfNotExists = async (
  productName: string,
  skuCode: string,
  organizationId: string,
  unitType: 'box' | 'loose' = 'box',
  transaction: Transaction
): Promise<{ productId: string; skuId: string }> => {
  console.log('🔍 Checking if product exists:', { productName, skuCode, organizationId });

  // First, try to find existing product by name
  let product = await Product.findOne({
    where: {
      name: productName,
      organization_id: organizationId
    },
    transaction
  });

  // If product doesn't exist, create it
  if (!product) {
    console.log('📦 Creating new product:', productName);
    product = await Product.create({
      organization_id: organizationId,
      name: productName,
      description: `Auto-created product: ${productName}`,
      status: 'active'
    }, { transaction });
  }

  // Now check if SKU exists for this product
  let sku = await SKU.findOne({
    where: {
      product_id: product.id,
      code: skuCode,
      organization_id: organizationId
    },
    transaction
  });

  // If SKU doesn't exist, create it
  if (!sku) {
    console.log('🏷️ Creating new SKU:', skuCode, 'for product:', productName);
    sku = await SKU.create({
      organization_id: organizationId,
      product_id: product.id,
      code: skuCode,
      unit_type: unitType,
      status: 'active'
    }, { transaction });
  }

  // Create or update inventory record with 0 quantity
  const existingInventory = await CurrentInventory.findOne({
    where: {
      product_id: product.id,
      sku_id: sku.id,
      organization_id: organizationId
    },
    transaction
  });

  if (!existingInventory) {
    console.log('📊 Creating inventory record with 0 quantity');
    await CurrentInventory.create({
      organization_id: organizationId,
      product_id: product.id,
      sku_id: sku.id,
      product_name: productName,
      sku_code: skuCode,
      unit_type: unitType,
      available_quantity: 0,
      total_weight: 0,
      last_updated_at: new Date()
    }, { transaction });
  }

  console.log('✅ Product and SKU ready:', { productId: product.id, skuId: sku.id });
  return { productId: product.id, skuId: sku.id };
};

// Helper function to create negative inventory entry
const createNegativeInventoryEntry = async (
  organizationId: string,
  productId: string,
  skuId: string,
  productName: string,
  skuCode: string,
  unitType: string,
  negativeQuantity: number,
  salesOrderId: string,
  transaction: Transaction
): Promise<void> => {
  console.log('⚠️ Creating negative inventory entry:', {
    productName,
    skuCode,
    negativeQuantity,
    salesOrderId
  });

  await NegativeInventory.create({
    organization_id: organizationId,
    product_id: productId,
    sku_id: skuId,
    product_name: productName,
    sku_code: skuCode,
    category: 'general', // Default category since categories are removed
    unit_type: unitType as 'box' | 'loose',
    negative_quantity: Math.abs(negativeQuantity),
    negative_weight: 0, // We'll calculate this later if needed
    reference_type: 'sales_order',
    reference_id: salesOrderId,
    notes: `Negative inventory created from sales order ${salesOrderId}`,
    status: 'pending'
  }, { transaction });
};

// Get all sales orders
export const getSalesOrders = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { startDate, endDate } = req.query;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Build where clause with date filtering
    const whereClause: any = { organization_id: organizationId };
    
    if (startDate && endDate) {
      // Make end date inclusive by adding one day and using less than
      const inclusiveEndDate = new Date(endDate as string);
      inclusiveEndDate.setDate(inclusiveEndDate.getDate() + 1);
      
      whereClause.order_date = {
        [Op.gte]: startDate,
        [Op.lt]: inclusiveEndDate.toISOString().split('T')[0]
      };
    } else if (startDate) {
      whereClause.order_date = {
        [Op.gte]: startDate
      };
    } else if (endDate) {
      // Make end date inclusive by adding one day and using less than
      const inclusiveEndDate = new Date(endDate as string);
      inclusiveEndDate.setDate(inclusiveEndDate.getDate() + 1);
      
      whereClause.order_date = {
        [Op.lt]: inclusiveEndDate.toISOString().split('T')[0]
      };
    }

    const salesOrders = await SalesOrder.findAll({
      where: whereClause,
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type', 'contact', 'email', 'credit_limit', 'current_balance']
        },
        {
          model: SalesOrderItem,
          as: 'items'
        },
        {
          model: SalesOrderPayment,
          as: 'payments'
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Transform the response to match frontend expectations
    const transformedOrders = salesOrders.map(order => {
      const orderData = order.toJSON();
      return {
        ...orderData,
        sales_order_items: orderData.items // Add alias for frontend compatibility
      };
    });

    res.json(transformedOrders);
  } catch (error) {
    console.error('Error fetching sales orders:', error);
    res.status(500).json({ error: 'Failed to fetch sales orders' });
  }
};

// Get single sales order
export const getSalesOrder = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const salesOrder = await SalesOrder.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type', 'contact', 'email', 'address', 'credit_limit', 'current_balance']
        },
        {
          model: SalesOrderItem,
          as: 'items'
        },
        {
          model: SalesOrderPayment,
          as: 'payments'
        },
        {
          model: SalesOrderDiscount,
          as: 'discounts',
          required: false
        }
      ]
    });
    
    if (!salesOrder) {
      return res.status(404).json({ error: 'Sales order not found' });
    }

    // Get return quantity information from all approved GRN requests (both returns and PDD)
    let pddReturnQuantity = 0;
    let pddReturnDetails = null;
    
    // Find all return/PDD requests for this sales order
    const grnRequests = await GRNReturnPDDRequest.findAll({
      where: {
        sales_order_id: id
      },
      include: [
        {
          model: GRNReturnPDDItem,
          as: 'items',
          include: [
            {
              model: SalesOrderItem,
              as: 'sales_order_item',
              attributes: ['product_name', 'sku_code', 'unit_type']
            }
          ]
        }
      ]
    });

    if (grnRequests.length > 0) {
      // Calculate total return quantity and create detailed breakdown
      pddReturnQuantity = grnRequests.reduce((total, request) => {
        return total + (request.items || []).reduce((itemTotal: number, item: any) => {
          return itemTotal + (item.return_quantity || 0);
        }, 0);
      }, 0);

      // Create detailed breakdown of returned items
      pddReturnDetails = grnRequests.map(request => ({
        request_id: request.id,
        request_type: request.request_type,
        requested_at: request.requested_at,
        items: (request.items || []).map((item: any) => ({
          product_name: item.sales_order_item?.product_name || 'Unknown Product',
          sku_code: item.sales_order_item?.sku_code || 'Unknown SKU',
          unit_type: item.sales_order_item?.unit_type || 'box',
          original_quantity: item.original_quantity,
          return_quantity: item.return_quantity,
          pdd_percentage: item.pdd_percentage,
          pdd_amount: item.pdd_amount
        }))
      }));
    }

    // Add PDD information to the response
    const responseData = {
      ...salesOrder.toJSON(),
      pdd_return_quantity: pddReturnQuantity,
      pdd_return_details: pddReturnDetails
    };

    res.json(responseData);
  } catch (error) {
    console.error('Error fetching sales order:', error);
    res.status(500).json({ error: 'Failed to fetch sales order' });
  }
};

// Get sales orders by customer ID
export const getSalesOrdersByCustomer = async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const salesOrders = await SalesOrder.findAll({
      where: { 
        customer_id: customerId,
        organization_id: organizationId 
      },
      include: [
        {
          model: SalesOrderItem,
          as: 'items'
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Transform the response to match frontend expectations
    const transformedOrders = salesOrders.map(order => {
      const orderData = order.toJSON();
      return {
        ...orderData,
        sales_order_items: orderData.items // Add alias for frontend compatibility
      };
    });

    res.json(transformedOrders);
  } catch (error) {
    console.error('Error fetching sales orders by customer:', error);
    res.status(500).json({ error: 'Failed to fetch sales orders' });
  }
};

// Create sales order with multiple payments
export const createSalesOrder = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const {
      order_number,
      customer_id,
      order_date,
      delivery_date,
      delivery_address,
      payment_terms,
      payment_mode,
      payment_status,
      subtotal,
      tax_amount,
      discount_amount,
      total_amount,
      status = 'pending',
      notes,
      items = [],
      payment_methods = []
    } = req.body;

    // Validate required fields
    if (!order_number || !customer_id || !order_date || !total_amount) {
      return res.status(400).json({ 
        error: 'Order number, customer ID, order date, and total amount are required' 
      });
    }

    // Validate customer exists
    const customer = await Customer.findOne({
      where: { 
        id: customer_id, 
        organization_id: organizationId 
      }
    });

    if (!customer) {
      return res.status(400).json({ error: 'Invalid customer ID' });
    }

    // Create sales order
    const salesOrder = await SalesOrder.create({
      organization_id: organizationId,
      order_number,
      customer_id,
      order_date,
      delivery_date,
      delivery_address,
      payment_terms,
      payment_mode,
      payment_status,
      subtotal,
      tax_amount,
      discount_amount,
      total_amount,
      status,
      notes
    }, { transaction });

    // Create sales order items
    if (items.length > 0) {
      const itemsData = items.map((item: any) => ({
        organization_id: organizationId,
        sales_order_id: salesOrder.id,
        product_id: item.product_id,
        sku_id: item.sku_id,
        product_name: item.product_name,
        sku_code: item.sku_code,
        quantity: item.quantity,
        unit_type: item.unit_type || 'box',
        unit_price: item.unit_price,
        total_price: item.total_price
      }));

      await SalesOrderItem.bulkCreate(itemsData, { transaction });
    }

    // Create sales order payments
    if (payment_methods.length > 0) {
      const paymentsData = payment_methods.map((payment: any) => ({
        organization_id: organizationId,
        sales_order_id: salesOrder.id,
        payment_type: payment.payment_type,
        amount: payment.amount,
        reference_number: payment.reference_number || null,
        proof_url: payment.proof_url || null,
        remarks: payment.remarks || null,
        status: payment.status || 'pending'
      }));

      await SalesOrderPayment.bulkCreate(paymentsData, { transaction });
    }

    // Update inventory and customer balance if needed
    if (status !== 'pending_approval') {
      // TODO: Update inventory logic here
      
      // Update customer balance for credit amount
      const creditAmount = total_amount - (payment_methods.reduce((sum: number, p: any) => sum + (p.amount || 0), 0));
      if (creditAmount > 0) {
        await customer.increment('current_balance', { 
          by: creditAmount, 
          transaction 
        });
      }
    }

    await transaction.commit();

    // Fetch the created sales order with associations
    const createdSalesOrder = await SalesOrder.findByPk(salesOrder.id, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type']
        },
        {
          model: SalesOrderItem,
          as: 'items'
        },
        {
          model: SalesOrderPayment,
          as: 'payments'
        }
      ]
    });
    
    res.status(201).json(createdSalesOrder);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating sales order:', error);
    
    // Handle duplicate order number error
    if ((error as any).name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({ error: 'Sales order number already exists' });
    }
    
    res.status(500).json({ 
      error: 'Failed to create sales order',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
};

// Test endpoint for debugging
export const testSalesOrderCreation = async (req: Request, res: Response) => {
  try {
    console.log('🧪 Test endpoint called');
    console.log('Headers:', req.headers);
    console.log('Body:', req.body);
    
    const organizationId = req.headers['x-organization-id'] as string;
    
    // Test customer lookup
    const customer = await Customer.findOne({
      where: { 
        id: '62377f36-e937-4a3b-a108-0b703f323d64', 
        organization_id: organizationId 
      }
    });
    
    console.log('Customer found:', !!customer);
    
    // Test SalesOrder creation
    const testOrder = {
      organization_id: organizationId,
      order_number: 'TEST-' + Date.now(),
      customer_id: '62377f36-e937-4a3b-a108-0b703f323d64',
      order_date: new Date(),
      payment_mode: 'credit',
      payment_status: 'unpaid',
      subtotal: 100,
      tax_amount: 0,
      discount_amount: 0,
      total_amount: 100,
      status: 'pending'
    };
    
    console.log('Creating test order with:', testOrder);
    const salesOrder = await SalesOrder.create(testOrder);
    console.log('Sales order created:', salesOrder.id);
    
    res.json({ 
      success: true, 
      message: 'Test successful',
      orderId: salesOrder.id,
      customer: !!customer
    });
  } catch (error) {
    console.error('Test endpoint error:', error);
    res.status(500).json({ 
      error: 'Test failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
};

// Update sales order
export const updateSalesOrder = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const {
      order_number,
      customer_id,
      order_date,
      delivery_date,
      delivery_address,
      payment_terms,
      payment_mode,
      payment_status,
      subtotal,
      tax_amount,
      discount_amount,
      total_amount,
      status,
      notes,
      items,
      payment_methods
    } = req.body;

    // Find sales order
    const salesOrder = await SalesOrder.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!salesOrder) {
      return res.status(404).json({ error: 'Sales order not found' });
    }

    // Update sales order
    const updateData: any = {};
    if (order_number !== undefined) updateData.order_number = order_number;
    if (customer_id !== undefined) updateData.customer_id = customer_id;
    if (order_date !== undefined) updateData.order_date = order_date;
    if (delivery_date !== undefined) updateData.delivery_date = delivery_date;
    if (delivery_address !== undefined) updateData.delivery_address = delivery_address;
    if (payment_terms !== undefined) updateData.payment_terms = payment_terms;
    if (payment_mode !== undefined) updateData.payment_mode = payment_mode;
    if (payment_status !== undefined) updateData.payment_status = payment_status;
    if (subtotal !== undefined) updateData.subtotal = subtotal;
    if (tax_amount !== undefined) updateData.tax_amount = tax_amount;
    if (discount_amount !== undefined) updateData.discount_amount = discount_amount;
    if (total_amount !== undefined) updateData.total_amount = total_amount;
    if (status !== undefined) updateData.status = status;
    if (notes !== undefined) updateData.notes = notes;

    await salesOrder.update(updateData, { transaction });

    // Update items if provided
    if (items !== undefined) {
      // Delete existing items
      await SalesOrderItem.destroy({
        where: { sales_order_id: id },
        transaction
      });

      // Create new items
      if (items.length > 0) {
        const itemsData = items.map((item: any) => ({
          organization_id: organizationId,
          sales_order_id: id,
          product_id: item.product_id,
          sku_id: item.sku_id,
          product_name: item.product_name,
          sku_code: item.sku_code,
          quantity: item.quantity,
          unit_type: item.unit_type || 'box',
          unit_price: item.unit_price,
          total_price: item.total_price
        }));

        await SalesOrderItem.bulkCreate(itemsData, { transaction });
      }
    }

    // Update payment methods if provided
    if (payment_methods !== undefined) {
      // Delete existing payments
      await SalesOrderPayment.destroy({
        where: { sales_order_id: id },
        transaction
      });

      // Create new payments
      if (payment_methods.length > 0) {
        const paymentsData = payment_methods.map((payment: any) => ({
          organization_id: organizationId,
          sales_order_id: id,
          payment_type: payment.payment_type,
          amount: payment.amount,
          reference_number: payment.reference_number || null,
          proof_url: payment.proof_url || null,
          remarks: payment.remarks || null,
          status: payment.status || 'pending'
        }));

        await SalesOrderPayment.bulkCreate(paymentsData, { transaction });
      }
    }

    await transaction.commit();

    // Fetch the updated sales order
    const updatedSalesOrder = await SalesOrder.findByPk(id, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type']
        },
        {
          model: SalesOrderItem,
          as: 'items'
        },
        {
          model: SalesOrderPayment,
          as: 'payments'
        }
      ]
    });
    
    res.json(updatedSalesOrder);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating sales order:', error);
    res.status(500).json({ error: 'Failed to update sales order' });
  }
};

// Cancel sales order
export const cancelSalesOrder = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find sales order with items
    const salesOrder = await SalesOrder.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: SalesOrderItem,
          as: 'items'
        },
        {
          model: Customer
        }
      ]
    });

    if (!salesOrder) {
      return res.status(404).json({ error: 'Sales order not found' });
    }

    // Update order status to cancelled
    await salesOrder.update({ status: 'cancelled' }, { transaction });

    // Restore inventory if order was processed (not pending approval)
    if (salesOrder.status !== 'pending_approval') {
      // TODO: Restore inventory logic here
      
      // Update customer balance if payment mode was credit
      if (salesOrder.payment_mode === 'credit' && salesOrder.customer) {
        await (salesOrder.customer as Customer).decrement('current_balance', { 
          by: salesOrder.total_amount || 0, 
          transaction 
        });
      }
    }

    await transaction.commit();
    
    res.json({ message: 'Sales order cancelled successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error cancelling sales order:', error);
    res.status(500).json({ error: 'Failed to cancel sales order' });
  }
};

// Delete sales order
export const deleteSalesOrder = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find sales order
    const salesOrder = await SalesOrder.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!salesOrder) {
      return res.status(404).json({ error: 'Sales order not found' });
    }

    // Delete related records (cascade should handle this, but being explicit)
    await SalesOrderPayment.destroy({
      where: { sales_order_id: id },
      transaction
    });

    await SalesOrderItem.destroy({
      where: { sales_order_id: id },
      transaction
    });

    await salesOrder.destroy({ transaction });

    await transaction.commit();
    
    res.json({ message: 'Sales order deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting sales order:', error);
    res.status(500).json({ error: 'Failed to delete sales order' });
  }
};

// Check inventory for sales order
export const checkInventoryForSalesOrder = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { items } = req.body;
    
    if (!items || !Array.isArray(items)) {
      return res.status(400).json({ error: 'Items array is required' });
    }

    const warnings = [];
    
    for (const item of items) {
      const { product_id, sku_id, quantity, product_name, sku_code } = item;
      
      if (!product_id || !sku_id || !quantity || quantity <= 0) {
        continue; // Skip invalid items
      }
      
      // Get current inventory
      const currentInventory = await CurrentInventory.findOne({
        where: { 
          product_id,
          sku_id,
          organization_id: organizationId 
        }
      });
      
      if (!currentInventory) {
        warnings.push({
          productId: product_id,
          skuId: sku_id,
          productName: product_name || 'Unknown Product',
          skuCode: sku_code || 'Unknown SKU',
          currentQuantity: 0,
          requestedQuantity: quantity,
          resultingQuantity: -quantity,
          type: 'not_found'
        });
        continue;
      }
      
      const currentQuantity = currentInventory.available_quantity || 0;
      const resultingQuantity = currentQuantity - quantity;
      
      if (resultingQuantity < 0) {
        warnings.push({
          productId: product_id,
          skuId: sku_id,
          productName: currentInventory.product_name,
          skuCode: currentInventory.sku_code,
          currentQuantity,
          requestedQuantity: quantity,
          resultingQuantity,
          type: 'negative'
        });
      }
    }
    
    res.json(warnings);
  } catch (error) {
    console.error('Error checking inventory:', error);
    res.status(500).json({ error: 'Failed to check inventory' });
  }
};

// Create sales order with multiple payments
export const createSalesOrderWithMultiplePayments = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    console.log('🚀 Sales order creation request received:', {
      organizationId,
      headers: req.headers,
      bodyKeys: Object.keys(req.body || {}),
      bodySize: JSON.stringify(req.body || {}).length
    });
    
    if (!organizationId) {
      console.log('❌ Missing organization ID');
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { orderData, paymentMethods } = req.body;
    
    if (!orderData) {
      console.log('❌ Missing order data');
      return res.status(400).json({ error: 'Order data is required' });
    }

    console.log('📋 Creating sales order with data:', {
      organizationId,
      orderData: { 
        ...orderData, 
        items: orderData.items?.length || 0,
        customer_id: orderData.customer_id,
        order_number: orderData.order_number,
        total_amount: orderData.total_amount
      },
      paymentMethodsCount: paymentMethods?.length || 0,
      paymentMethods: paymentMethods
    });

    // Validate customer exists
    const customer = await Customer.findOne({
      where: { 
        id: orderData.customer_id, 
        organization_id: organizationId 
      }
    });

    if (!customer) {
      return res.status(400).json({ error: 'Invalid customer ID' });
    }

    // Create the sales order with proper defaults for required fields
    const salesOrder = await SalesOrder.create({
      organization_id: organizationId,
      order_number: orderData.order_number,
      customer_id: orderData.customer_id,
      order_date: orderData.order_date,
      delivery_date: orderData.delivery_date || null,
      delivery_address: orderData.delivery_address || null,
      payment_terms: orderData.payment_terms || 30,
      payment_mode: orderData.payment_mode || 'credit',
      payment_status: orderData.payment_status || 'unpaid',
      subtotal: orderData.subtotal || 0,
      tax_amount: orderData.tax_amount || 0,
      discount_amount: orderData.discount_amount || 0,
      total_amount: orderData.total_amount || 0,
      status: orderData.status || 'pending',
      notes: orderData.notes || null
    }, { transaction });

    // Create sales order items (handle new products) and store processed items for inventory updates
    const processedItems = [];
    if (orderData.items && orderData.items.length > 0) {
      const itemsData = [];
      
      for (const item of orderData.items) {
        let productId = item.productId || item.product_id;
        let skuId = item.skuId || item.sku_id;
        const productName = item.productName || item.product_name;
        const skuCode = item.skuCode || item.sku_code;
        const unitType = item.unitType || item.unit_type || 'box';
        
        // Check if this is a new product (indicated by special format or missing IDs)
        const isNewProduct = !productId || !skuId || productId === 'NEW_PRODUCT';
        
        if (isNewProduct && productName && skuCode) {
          console.log('🆕 Creating new product for sales order:', { productName, skuCode });
          
          // Create product and SKU
          const { productId: newProductId, skuId: newSkuId } = await createProductAndSKUIfNotExists(
            productName,
            skuCode,
            organizationId,
            unitType as 'box' | 'loose',
            transaction
          );
          
          productId = newProductId;
          skuId = newSkuId;
        }
        
        // Store processed item data for inventory updates
        processedItems.push({
          productId,
          skuId,
          productName,
          skuCode,
          unitType,
          quantity: item.quantity
        });
        
        itemsData.push({
          organization_id: organizationId,
          sales_order_id: salesOrder.id,
          product_id: productId,
          sku_id: skuId,
          product_name: productName,
          sku_code: skuCode,
          quantity: item.quantity,
          unit_type: unitType,
          unit_price: item.unitPrice || item.unit_price,
          total_price: item.totalPrice || item.total_price
        });
      }

      console.log('Creating sales order items with data:', itemsData);
      await SalesOrderItem.bulkCreate(itemsData, { transaction });
    }

    // Create sales order payments
    if (paymentMethods && paymentMethods.length > 0) {
      const paymentsData = paymentMethods.map((payment: any) => ({
        organization_id: organizationId,
        sales_order_id: salesOrder.id,
        payment_type: payment.type || payment.payment_type,
        amount: payment.amount,
        reference_number: payment.reference_number || null,
        proof_url: payment.proof_url || null,
        remarks: payment.remarks || null,
        status: payment.status || 'pending'
      }));

      console.log('Creating payments with data:', paymentsData);
      await SalesOrderPayment.bulkCreate(paymentsData, { transaction });
    }

    // Update inventory and customer balance if order is not pending approval
    if (orderData.status !== 'pending_approval') {
      // Update inventory for each processed item and handle negative inventory
      if (processedItems.length > 0) {
        for (const item of processedItems) {
          const { productId, skuId, productName, skuCode, unitType, quantity } = item;

          // Get current inventory before updating
          const currentInventory = await CurrentInventory.findOne({
            where: { 
              product_id: productId,
              sku_id: skuId,
              organization_id: organizationId 
            },
            transaction
          });

          const currentQuantity = currentInventory?.available_quantity || 0;
          const resultingQuantity = currentQuantity - quantity;

          // Update inventory
          await CurrentInventory.decrement('available_quantity', {
            by: quantity,
            where: { 
              product_id: productId,
              sku_id: skuId,
              organization_id: organizationId 
            },
            transaction
          });

          // Create inventory transaction record using the correct IDs
          await InventoryTransactionService.createSaleTransaction(
            organizationId,
            productId,
            skuId,
            quantity,
            salesOrder.id,
            undefined, // performed_by - we can add this later if needed
            `Sale transaction for order ${orderData.order_number}`,
            transaction
          );

          // If inventory goes negative, create negative inventory entry
          if (resultingQuantity < 0) {
            await createNegativeInventoryEntry(
              organizationId,
              productId,
              skuId,
              productName,
              skuCode,
              unitType,
              Math.abs(resultingQuantity),
              salesOrder.id,
              transaction
            );
          }
        }
      }

      // Update customer balance for credit amount
      const totalPaidAmount = (paymentMethods || [])
        .filter((method: any) => method.type !== 'credit_increase')
        .reduce((sum: number, method: any) => sum + (method.amount || 0), 0);
      
      const creditAmount = orderData.total_amount - totalPaidAmount;
      
      if (creditAmount > 0) {
        await Customer.increment('current_balance', {
          by: creditAmount,
          where: { 
            id: orderData.customer_id,
            organization_id: organizationId 
          },
          transaction
        });
      }
    }

    await transaction.commit();

    // Fetch the created sales order with associations
    const createdSalesOrder = await SalesOrder.findByPk(salesOrder.id, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type']
        },
        {
          model: SalesOrderItem,
          as: 'items'
        },
        {
          model: SalesOrderPayment,
          as: 'payments'
        }
      ]
    });
    
    res.status(201).json(createdSalesOrder);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating sales order with multiple payments:', error);
    
    // Provide more detailed error information
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      
      // Handle specific Sequelize errors
      if ((error as any).name === 'SequelizeValidationError') {
        const validationErrors = (error as any).errors?.map((e: any) => ({
          field: e.path,
          message: e.message,
          value: e.value
        }));
        return res.status(400).json({ 
          error: 'Validation error', 
          details: validationErrors 
        });
      }
      
      if ((error as any).name === 'SequelizeUniqueConstraintError') {
        return res.status(400).json({ error: 'Sales order number already exists' });
      }
      
      if ((error as any).name === 'SequelizeForeignKeyConstraintError') {
        return res.status(400).json({ error: 'Invalid reference to customer or organization' });
      }
    }
    
    res.status(500).json({ error: 'Failed to create sales order' });
  }
};

// Update dispatch details
export const updateSalesOrderDispatchDetails = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const {
      vehicle_number,
      driver_name,
      driver_contact,
      delivery_location_confirmed,
      items
    } = req.body;

    // Find sales order
    const salesOrder = await SalesOrder.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: SalesOrderItem,
          as: 'items'
        }
      ]
    });

    if (!salesOrder) {
      return res.status(404).json({ error: 'Sales order not found' });
    }

    // Update item quantities and recalculate totals
    let newSubtotal = 0;
    
    if (items && items.length > 0) {
      for (const itemUpdate of items) {
        const currentItem = await SalesOrderItem.findByPk(itemUpdate.id);
        if (currentItem) {
          const newTotalPrice = itemUpdate.final_loaded_quantity * currentItem.unit_price;
          
          await currentItem.update({
            quantity: itemUpdate.final_loaded_quantity,
            total_price: newTotalPrice
          }, { transaction });
          
          newSubtotal += newTotalPrice;
        }
      }
    }

    // Calculate new total amount
    const newTotalAmount = newSubtotal - (salesOrder.discount_amount || 0);

    // Update the sales order with dispatch details and new totals
    await salesOrder.update({
      status: 'dispatched',
      vehicle_number,
      driver_name,
      driver_contact,
      delivery_location_confirmed,
      subtotal: newSubtotal,
      total_amount: newTotalAmount
    }, { transaction });

    await transaction.commit();

    // Fetch updated sales order
    const updatedSalesOrder = await SalesOrder.findByPk(id, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type']
        },
        {
          model: SalesOrderItem,
          as: 'items'
        }
      ]
    });
    
    res.json(updatedSalesOrder);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating dispatch details:', error);
    res.status(500).json({ error: 'Failed to update dispatch details' });
  }
};

// Get outstation sales orders
export const getOutstationSalesOrders = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    console.log('Fetching outstation sales orders for organization:', organizationId);

    const salesOrders = await SalesOrder.findAll({
      where: { 
        organization_id: organizationId,
        status: ['dispatch_pending', 'dispatched']
      },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type', 'contact', 'email']
        },
        {
          model: SalesOrderItem,
          as: 'items',
          required: false // Allow orders without items for debugging
        }
      ],
      order: [['created_at', 'DESC']]
    });

    console.log('Found outstation orders:', {
      count: salesOrders.length,
      orders: salesOrders.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        itemsCount: order.items?.length || 0,
        hasItems: !!order.items,
        items: order.items
      }))
    });

    res.json(salesOrders);
  } catch (error) {
    console.error('Error fetching outstation sales orders:', error);
    res.status(500).json({ error: 'Failed to fetch outstation sales orders' });
  }
};


// Get sales analytics
export const getSalesAnalytics = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Get all sales orders excluding drafts, cancelled, and pending approval
    const validStatuses = ['processing', 'dispatched', 'delivered', 'completed'];
    const processingStatuses = ['processing', 'dispatched'];
    const completedStatuses = ['delivered', 'completed'];

    // Calculate total sales value (only completed/delivered orders)
    const completedSalesResult = await SalesOrder.findAll({
      where: { 
        organization_id: organizationId,
        status: completedStatuses
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('total_amount')), 'totalValue'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalCount']
      ],
      raw: true
    });

    // Calculate processing sales value
    const processingSalesResult = await SalesOrder.findAll({
      where: { 
        organization_id: organizationId,
        status: processingStatuses
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('total_amount')), 'totalValue'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalCount']
      ],
      raw: true
    });

    // Get order counts by status
    const ordersByStatus = await SalesOrder.findAll({
      where: { 
        organization_id: organizationId,
        status: { [Op.notIn]: ['draft', 'pending_approval'] }
      },
      attributes: [
        'status',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // Get sales by payment mode
    const salesByPaymentMode = await SalesOrder.findAll({
      where: { 
        organization_id: organizationId,
        status: validStatuses
      },
      attributes: [
        'payment_mode',
        [sequelize.fn('SUM', sequelize.col('total_amount')), 'totalValue'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      group: ['payment_mode'],
      raw: true
    });

    // Get total orders count (excluding drafts, cancelled, pending approval)
    const totalOrdersResult = await SalesOrder.count({
      where: { 
        organization_id: organizationId,
        status: validStatuses
      }
    });

    // Format the response
    const completedSales = completedSalesResult[0] as any;
    const processingSales = processingSalesResult[0] as any;

    // Convert status counts to object
    const statusCounts: { [key: string]: number } = {};
    ordersByStatus.forEach((item: any) => {
      statusCounts[item.status] = parseInt(item.count);
    });

    // Convert payment mode data to object
    const paymentModeData: { [key: string]: { value: number; count: number } } = {};
    salesByPaymentMode.forEach((item: any) => {
      paymentModeData[item.payment_mode] = {
        value: parseFloat(item.totalValue) || 0,
        count: parseInt(item.count) || 0
      };
    });

    const analytics = {
      totalSalesValue: parseFloat(completedSales?.totalValue) || 0,
      totalProcessingValue: parseFloat(processingSales?.totalValue) || 0,
      totalOrders: totalOrdersResult,
      completedOrders: parseInt(completedSales?.totalCount) || 0,
      processingOrders: parseInt(processingSales?.totalCount) || 0,
      ordersByStatus: {
        processing: statusCounts.processing || 0,
        dispatched: statusCounts.dispatched || 0,
        delivered: statusCounts.delivered || 0,
        completed: statusCounts.completed || 0,
        cancelled: statusCounts.cancelled || 0
      },
      salesByPaymentMode: {
        cash: paymentModeData.cash || { value: 0, count: 0 },
        credit: paymentModeData.credit || { value: 0, count: 0 },
        online: paymentModeData.online || { value: 0, count: 0 },
        upi: paymentModeData.upi || { value: 0, count: 0 },
        multiple: paymentModeData.multiple || { value: 0, count: 0 }
      }
    };

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching sales analytics:', error);
    res.status(500).json({ error: 'Failed to fetch sales analytics' });
  }
};
