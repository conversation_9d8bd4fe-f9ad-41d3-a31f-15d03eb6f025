import { Request, Response } from 'express';
import { Transaction, Op } from 'sequelize';
import sequelize from '../config/sequelize';
import { Customer, CreateCustomerData, UpdateCustomerData } from '../models/Customer';
import { Organization } from '../models/Organization';

// Get all customers for an organization
export const getCustomers = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const customers = await Customer.findAll({
      where: { organization_id: organizationId },
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Ensure numeric fields are properly converted to numbers
    const serializedCustomers = customers.map(customer => {
      const customerData = customer.toJSON();
      return {
        ...customerData,
        credit_limit: parseFloat(customerData.credit_limit) || 0,
        current_balance: parseFloat(customerData.current_balance) || 0,
        payment_terms: parseInt(customerData.payment_terms) || 30
      };
    });

    res.json(serializedCustomers);
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ error: 'Failed to fetch customers' });
  }
};

// Get a single customer
export const getCustomer = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const customer = await Customer.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Ensure numeric fields are properly converted to numbers
    const customerData = customer.toJSON();
    const serializedCustomer = {
      ...customerData,
      credit_limit: parseFloat(customerData.credit_limit) || 0,
      current_balance: parseFloat(customerData.current_balance) || 0,
      payment_terms: parseInt(customerData.payment_terms) || 30
    };

    res.json(serializedCustomer);
  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({ error: 'Failed to fetch customer' });
  }
};

// Create a new customer
export const createCustomer = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const customerData: CreateCustomerData = {
      ...req.body,
      organization_id: organizationId
    };

    // Validate required fields
    if (!customerData.name) {
      return res.status(400).json({ 
        error: 'Customer name is required' 
      });
    }

    // Validate organization exists
    const organization = await Organization.findByPk(organizationId);
    if (!organization) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    const customer = await Customer.create(customerData as any);

    // Fetch the created customer with associations
    const createdCustomer = await Customer.findByPk(customer.id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.status(201).json(createdCustomer);
  } catch (error) {
    console.error('Error creating customer:', error);
    
    // Handle duplicate email error
    if ((error as any).name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({ error: 'Customer with this email already exists' });
    }
    
    res.status(500).json({ error: 'Failed to create customer' });
  }
};

// Update a customer
export const updateCustomer = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const updateData: UpdateCustomerData = req.body;

    // Find customer
    const customer = await Customer.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Update customer
    await customer.update(updateData);

    // Fetch the updated customer with associations
    const updatedCustomer = await Customer.findByPk(id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.json(updatedCustomer);
  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({ error: 'Failed to update customer' });
  }
};

// Delete a customer
export const deleteCustomer = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find customer
    const customer = await Customer.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    await customer.destroy();

    res.json({ message: 'Customer deleted successfully' });
  } catch (error) {
    console.error('Error deleting customer:', error);
    res.status(500).json({ error: 'Failed to delete customer' });
  }
};

// Update customer balance
export const updateCustomerBalance = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { amount, operation } = req.body;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!amount || !operation || !['add', 'subtract'].includes(operation)) {
      return res.status(400).json({ 
        error: 'Amount and valid operation (add/subtract) are required' 
      });
    }

    // Find customer
    const customer = await Customer.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      transaction
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // Update balance
    if (operation === 'add') {
      await customer.increment('current_balance', { by: amount, transaction });
    } else {
      await customer.decrement('current_balance', { by: amount, transaction });
    }

    await transaction.commit();

    // Fetch updated customer
    const updatedCustomer = await Customer.findByPk(id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.json(updatedCustomer);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating customer balance:', error);
    res.status(500).json({ error: 'Failed to update customer balance' });
  }
};

// Get sales orders by customer ID
export const getSalesOrdersByCustomerId = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // First verify customer exists and belongs to organization
    const customer = await Customer.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // TODO: Implement sales orders relationship when SalesOrder model is available
    // For now, return empty array
    const salesOrders: any[] = [];

    res.json(salesOrders);
  } catch (error) {
    console.error('Error fetching sales orders by customer ID:', error);
    res.status(500).json({ error: 'Failed to fetch sales orders' });
  }
};

// Get payments by customer ID
export const getPaymentsByCustomerId = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // First verify customer exists and belongs to organization
    const customer = await Customer.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    // TODO: Implement payments relationship when Payment model is available
    // For now, return empty array
    const payments: any[] = [];

    res.json(payments);
  } catch (error) {
    console.error('Error fetching payments by customer ID:', error);
    res.status(500).json({ error: 'Failed to fetch payments' });
  }
};

// Get customers with statistics
export const getCustomersWithStats = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const customers = await Customer.findAll({
      where: { organization_id: organizationId },
      attributes: [
        'id',
        'name',
        'customer_type',
        'contact',
        'email',
        'current_balance',
        'credit_limit',
        'status',
        'created_at'
      ],
      order: [['created_at', 'DESC']]
    });

    // TODO: Add sales orders count and total amount when models are available
    const customersWithStats = customers.map(customer => ({
      ...customer.toJSON(),
      sales_orders_count: 0,
      total_sales_amount: 0,
      last_order_date: null
    }));

    res.json(customersWithStats);
  } catch (error) {
    console.error('Error fetching customers with stats:', error);
    res.status(500).json({ error: 'Failed to fetch customers with statistics' });
  }
};

// Search customers
export const searchCustomers = async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const customers = await Customer.findAll({
      where: {
        organization_id: organizationId,
        [Op.or]: [
          { name: { [Op.like]: `%${q}%` } },
          { contact: { [Op.like]: `%${q}%` } },
          { email: { [Op.like]: `%${q}%` } }
        ]
      },
      attributes: ['id', 'name', 'customer_type', 'contact', 'email', 'status'],
      limit: 20,
      order: [['name', 'ASC']]
    });

    res.json(customers);
  } catch (error) {
    console.error('Error searching customers:', error);
    res.status(500).json({ error: 'Failed to search customers' });
  }
};
