import { Request, Response } from 'express';
import { Transaction, Op } from 'sequelize';
import sequelize from '../config/sequelize';
import { Payment, CustomerCreditExtension, CreatePaymentData, UpdatePaymentData, CreateCreditExtensionData, UpdateCreditExtensionData } from '../models/Payment';
import { Customer } from '../models/Customer';
import { Supplier } from '../models/Supplier';
import { Organization } from '../models/Organization';

// Get all payments
export const getPayments = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const payments = await Payment.findAll({
      where: { organization_id: organizationId },
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ],
      order: [['payment_date', 'DESC']]
    });

    res.json(payments);
  } catch (error) {
    console.error('Error fetching payments:', error);
    res.status(500).json({ error: 'Failed to fetch payments' });
  }
};

// Get single payment
export const getPayment = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const payment = await Payment.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    res.json(payment);
  } catch (error) {
    console.error('Error fetching payment:', error);
    res.status(500).json({ error: 'Failed to fetch payment' });
  }
};

// Create payment
export const createPayment = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Handle both JSON and FormData requests
    let paymentData: any;
    
    console.log('=== BACKEND PAYMENT DEBUG ===');
    console.log('Request body:', req.body);
    console.log('Has req.body.data:', !!req.body.data);
    console.log('Content-Type:', req.headers['content-type']);
    
    if (req.body.data) {
      // FormData request - parse the JSON data
      try {
        console.log('Parsing FormData JSON:', req.body.data);
        const parsedData = JSON.parse(req.body.data);
        console.log('Parsed data:', parsedData);
        paymentData = {
          ...parsedData,
          organization_id: organizationId
        };
      } catch (parseError) {
        console.error('Error parsing FormData JSON:', parseError);
        console.error('Raw data that failed to parse:', req.body.data);
        return res.status(400).json({ 
          error: 'Invalid JSON data in FormData',
          raw_data: req.body.data,
          parse_error: parseError instanceof Error ? parseError.message : 'Unknown parse error'
        });
      }
    } else {
      // Regular JSON request
      paymentData = {
        ...req.body,
        organization_id: organizationId
      };
    }

    console.log('Final payment data after parsing:', paymentData);

    // Validate required fields - check both possible field name formats and handle empty strings
    const type = paymentData.type?.trim();
    const amount = paymentData.amount;
    const paymentDate = paymentData.payment_date || paymentData.paymentDate;
    const mode = paymentData.mode?.trim();
    const status = paymentData.status?.trim() || 'completed'; // Default to completed

    console.log('Payment validation - extracted fields:', { 
      type: `"${type}" (${typeof type})`,
      amount: `"${amount}" (${typeof amount})`,
      paymentDate: `"${paymentDate}" (${typeof paymentDate})`,
      mode: `"${mode}" (${typeof mode})`,
      status: `"${status}" (${typeof status})`
    });

    // Check for missing or empty required fields
    const missingFields: string[] = [];
    const fieldDetails: any = {};
    
    if (!type || type === '') {
      missingFields.push('type');
      fieldDetails.type = 'MISSING_OR_EMPTY';
    } else {
      fieldDetails.type = type;
    }
    
    if (!amount || amount === '' || amount === 0) {
      missingFields.push('amount');
      fieldDetails.amount = 'MISSING_OR_EMPTY_OR_ZERO';
    } else {
      fieldDetails.amount = amount;
    }
    
    if (!paymentDate || paymentDate === '') {
      missingFields.push('payment_date');
      fieldDetails.payment_date = 'MISSING_OR_EMPTY';
    } else {
      fieldDetails.payment_date = paymentDate;
    }
    
    if (!mode || mode === '') {
      missingFields.push('mode');
      fieldDetails.mode = 'MISSING_OR_EMPTY';
    } else {
      fieldDetails.mode = mode;
    }

    if (missingFields.length > 0) {
      console.log('Validation failed - missing required fields:', missingFields);
      console.log('Field details:', fieldDetails);
      return res.status(400).json({ 
        error: `Missing required fields: ${missingFields.join(', ')}`,
        field_details: fieldDetails,
        received_raw_data: paymentData
      });
    }

    console.log('All required fields present, proceeding with validation...');

    // Validate date format
    let parsedDate;
    try {
      parsedDate = new Date(paymentDate);
      if (isNaN(parsedDate.getTime())) {
        throw new Error('Invalid date');
      }
    } catch (error) {
      console.log('Invalid date format:', paymentDate);
      return res.status(400).json({ 
        error: 'Invalid payment date format. Please provide a valid date.',
        received_date: paymentDate
      });
    }

    // Validate status if provided
    const validStatuses = ['pending', 'confirmed', 'failed', 'completed'];
    if (status && !validStatuses.includes(status)) {
      console.log('Invalid status:', status, 'Valid statuses:', validStatuses);
      return res.status(400).json({ 
        error: `Invalid status. Must be one of: ${validStatuses.join(', ')}`,
        received_status: status
      });
    }

    // Normalize payment type - convert 'made' to 'paid' for internal consistency
    const normalizedType = type === 'made' ? 'paid' : type;

    // Normalize the payment data to use consistent field names
    const normalizedPaymentData: CreatePaymentData = {
      organization_id: organizationId,
      type: normalizedType as 'received' | 'paid' | 'made' | 'expense',
      amount: parseFloat(amount),
      payment_date: new Date(paymentDate),
      party_id: paymentData.party_id || paymentData.partyId,
      party_type: paymentData.party_type || paymentData.partyType,
      party_name: paymentData.party_name || paymentData.partyName || '',
      reference_id: paymentData.reference_id || paymentData.referenceId,
      reference_type: paymentData.reference_type || paymentData.referenceType,
      reference_number: paymentData.reference_number || paymentData.referenceNumber || '',
      mode: mode,
      status: status || 'pending',
      notes: paymentData.notes
    };

    console.log('Normalized payment data:', normalizedPaymentData);

    // Party ID and party type are required for received and paid/made payments, but not for expenses
    if (normalizedPaymentData.type !== 'expense' && (!normalizedPaymentData.party_id || !normalizedPaymentData.party_type)) {
      return res.status(400).json({ 
        error: 'Party ID and party type are required for received and paid payments' 
      });
    }

    // Validate organization exists
    const organization = await Organization.findByPk(organizationId);
    if (!organization) {
      return res.status(400).json({ error: 'Invalid organization ID' });
    }

    // Validate party exists
    if (normalizedPaymentData.party_type === 'customer') {
      const customer = await Customer.findOne({
        where: { 
          id: normalizedPaymentData.party_id,
          organization_id: organizationId 
        }
      });
      if (!customer) {
        return res.status(400).json({ error: 'Invalid customer ID' });
      }
    } else if (normalizedPaymentData.party_type === 'supplier') {
      const supplier = await Supplier.findOne({
        where: { 
          id: normalizedPaymentData.party_id,
          organization_id: organizationId 
        }
      });
      if (!supplier) {
        return res.status(400).json({ error: 'Invalid supplier ID' });
      }
    }

    // TODO: Handle file upload if present
    // For now, we'll skip file storage since the database schema doesn't include file fields
    // In a production environment, you would save the file to a storage service
    // and store the file URL/path in the payment record
    
    const payment = await Payment.create(normalizedPaymentData as any, { transaction });

    // Update party balance based on payment type
    if (normalizedPaymentData.party_type === 'customer' && normalizedPaymentData.type === 'received') {
      // Payment received from customer - reduce their outstanding balance
      await Customer.decrement('current_balance', {
        by: normalizedPaymentData.amount,
        where: { id: normalizedPaymentData.party_id },
        transaction
      });
    } else if (normalizedPaymentData.party_type === 'supplier' && normalizedPaymentData.type === 'paid') {
      // Payment made to supplier - reduce their outstanding balance
      await Supplier.decrement('current_balance', {
        by: normalizedPaymentData.amount,
        where: { id: normalizedPaymentData.party_id },
        transaction
      });
    }

    await transaction.commit();

    // Fetch the created payment with associations
    const createdPayment = await Payment.findByPk(payment.id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.status(201).json(createdPayment);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating payment:', error);
    res.status(500).json({ error: 'Failed to create payment' });
  }
};

// Update payment
export const updatePayment = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const updateData: UpdatePaymentData = req.body;

    // Find payment
    const payment = await Payment.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    // Update payment
    await payment.update(updateData);

    // Fetch the updated payment with associations
    const updatedPayment = await Payment.findByPk(id, {
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    res.json(updatedPayment);
  } catch (error) {
    console.error('Error updating payment:', error);
    res.status(500).json({ error: 'Failed to update payment' });
  }
};

// Delete payment
export const deletePayment = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find payment
    const payment = await Payment.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      transaction
    });

    if (!payment) {
      return res.status(404).json({ error: 'Payment not found' });
    }

    // Reverse balance updates
    if (payment.party_type === 'customer' && payment.type === 'received') {
      // Reverse payment received - increase customer balance
      await Customer.increment('current_balance', {
        by: payment.amount,
        where: { id: payment.party_id },
        transaction
      });
    } else if (payment.party_type === 'supplier' && (payment.type === 'paid' || payment.type === 'made')) {
      // Reverse payment made - increase supplier balance (handle both 'paid' and 'made')
      await Supplier.increment('current_balance', {
        by: payment.amount,
        where: { id: payment.party_id },
        transaction
      });
    }

    await payment.destroy({ transaction });
    await transaction.commit();

    res.json({ message: 'Payment deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting payment:', error);
    res.status(500).json({ error: 'Failed to delete payment' });
  }
};

// Get payments by party ID
export const getPaymentsByPartyId = async (req: Request, res: Response) => {
  try {
    // Extract party ID from different possible parameter names
    const { partyId, customerId, supplierId } = req.params;
    const actualPartyId = partyId || customerId || supplierId;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!actualPartyId) {
      return res.status(400).json({ error: 'Party ID is required' });
    }

    const payments = await Payment.findAll({
      where: { 
        party_id: actualPartyId,
        organization_id: organizationId 
      },
      order: [['payment_date', 'DESC']]
    });

    res.json(payments);
  } catch (error) {
    console.error('Error fetching payments by party ID:', error);
    res.status(500).json({ error: 'Failed to fetch payments' });
  }
};

// Get payments by reference ID (e.g., sales order ID)
export const getPaymentsByReferenceId = async (req: Request, res: Response) => {
  try {
    const { referenceId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!referenceId) {
      return res.status(400).json({ error: 'Reference ID is required' });
    }

    const payments = await Payment.findAll({
      where: { 
        reference_id: referenceId,
        organization_id: organizationId 
      },
      include: [
        {
          model: Organization,
          attributes: ['id', 'name', 'code']
        }
      ],
      order: [['payment_date', 'DESC']]
    });

    res.json(payments);
  } catch (error) {
    console.error('Error fetching payments by reference ID:', error);
    res.status(500).json({ error: 'Failed to fetch payments' });
  }
};

// Customer Credit Extensions

// Get customer credit extensions
export const getCustomerCreditExtensions = async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Verify customer belongs to organization
    const customer = await Customer.findOne({
      where: { 
        id: customerId,
        organization_id: organizationId 
      }
    });

    if (!customer) {
      return res.status(404).json({ error: 'Customer not found' });
    }

    const extensions = await CustomerCreditExtension.findAll({
      where: { 
        customer_id: customerId,
        organization_id: organizationId 
      },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json(extensions);
  } catch (error) {
    console.error('Error fetching customer credit extensions:', error);
    res.status(500).json({ error: 'Failed to fetch customer credit extensions' });
  }
};

// Create customer credit extension
export const createCustomerCreditExtension = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const extensionData: CreateCreditExtensionData = {
      ...req.body,
      organization_id: organizationId
    };

    // Validate required fields
    if (!extensionData.customer_id || !extensionData.amount) {
      return res.status(400).json({ 
        error: 'Customer ID and amount are required' 
      });
    }

    // Verify customer belongs to organization
    const customer = await Customer.findOne({
      where: { 
        id: extensionData.customer_id,
        organization_id: organizationId 
      }
    });

    if (!customer) {
      return res.status(400).json({ error: 'Invalid customer ID' });
    }

    const extension = await CustomerCreditExtension.create(extensionData as any);

    // Fetch the created extension with associations
    const createdExtension = await CustomerCreditExtension.findByPk(extension.id, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type']
        }
      ]
    });

    res.status(201).json(createdExtension);
  } catch (error) {
    console.error('Error creating customer credit extension:', error);
    res.status(500).json({ error: 'Failed to create customer credit extension' });
  }
};

// Update customer credit extension
export const updateCustomerCreditExtension = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const updateData: UpdateCreditExtensionData = req.body;

    // Find extension
    const extension = await CustomerCreditExtension.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      transaction
    });

    if (!extension) {
      return res.status(404).json({ error: 'Credit extension not found' });
    }

    // If approving the extension, update customer credit limit
    if (updateData.status === 'approved' && extension.status !== 'approved') {
      await Customer.increment('credit_limit', {
        by: extension.amount,
        where: { id: extension.customer_id },
        transaction
      });

      updateData.approved_at = new Date();
    }

    // Update extension
    await extension.update(updateData, { transaction });
    await transaction.commit();

    // Fetch the updated extension with associations
    const updatedExtension = await CustomerCreditExtension.findByPk(id, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type']
        }
      ]
    });

    res.json(updatedExtension);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating customer credit extension:', error);
    res.status(500).json({ error: 'Failed to update customer credit extension' });
  }
};

// Get all credit extensions for organization
export const getAllCreditExtensions = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const extensions = await CustomerCreditExtension.findAll({
      where: { organization_id: organizationId },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'customer_type']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    res.json(extensions);
  } catch (error) {
    console.error('Error fetching all credit extensions:', error);
    res.status(500).json({ error: 'Failed to fetch credit extensions' });
  }
};

// Search payments
export const searchPayments = async (req: Request, res: Response) => {
  try {
    const { q } = req.query;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Search query is required' });
    }

    const payments = await Payment.findAll({
      where: {
        organization_id: organizationId,
        [Op.or]: [
          { party_name: { [Op.like]: `%${q}%` } },
          { reference_number: { [Op.like]: `%${q}%` } },
          { notes: { [Op.like]: `%${q}%` } }
        ]
      },
      attributes: ['id', 'type', 'amount', 'payment_date', 'party_name', 'party_type', 'mode', 'status'],
      limit: 20,
      order: [['payment_date', 'DESC']]
    });

    res.json(payments);
  } catch (error) {
    console.error('Error searching payments:', error);
    res.status(500).json({ error: 'Failed to search payments' });
  }
};

// Get payment statistics
export const getPaymentStatistics = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { startDate, endDate } = req.query;

    let whereClause: any = { organization_id: organizationId };

    if (startDate && endDate) {
      whereClause.payment_date = {
        [Op.between]: [startDate, endDate]
      };
    }

    const [totalReceived, totalPaid, totalCount] = await Promise.all([
      Payment.sum('amount', {
        where: { ...whereClause, type: 'received' }
      }),
      Payment.sum('amount', {
        where: { ...whereClause, type: 'paid' }
      }),
      Payment.count({
        where: whereClause
      })
    ]);

    res.json({
      total_received: totalReceived || 0,
      total_paid: totalPaid || 0,
      net_amount: (totalReceived || 0) - (totalPaid || 0),
      total_transactions: totalCount
    });
  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    res.status(500).json({ error: 'Failed to fetch payment statistics' });
  }
};
