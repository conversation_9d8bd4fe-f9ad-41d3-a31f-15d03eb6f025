import { Request, Response } from 'express';
import { Transaction } from 'sequelize';
import sequelize from '../config/sequelize';
import { 
  SalesOrder, 
  SalesOrderItem, 
  GRNReturnPDDRequest, 
  GRNReturnPDDItem,
  Customer,
  CurrentInventory
} from '../models';
import { InventoryTransactionService } from '../services/inventoryTransactionService';

// Mark sales order as GRN Complete
export const markGRNComplete = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const salesOrder = await SalesOrder.findOne({
      where: { 
        id, 
        organization_id: organizationId, 
        status: ['completed', 'dispatched'] 
      }
    });

    if (!salesOrder) {
      return res.status(404).json({ error: 'Completed or dispatched sales order not found' });
    }

    await salesOrder.update({ status: 'grn_complete' });
    
    res.json({ 
      message: 'Order marked as GRN Complete successfully',
      order: salesOrder
    });
  } catch (error) {
    console.error('Error marking GRN complete:', error);
    res.status(500).json({ error: 'Failed to mark GRN complete' });
  }
};

// Create Return/PDD request
export const createReturnPDDRequest = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { sales_order_id, request_type, items, notes } = req.body;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Validate sales order is in completed status
    const salesOrder = await SalesOrder.findOne({
      where: { 
        id: sales_order_id, 
        organization_id: organizationId, 
        status: ['completed', 'dispatched']
      },
      include: [
        {
          model: SalesOrderItem,
          as: 'items'
        },
        {
          model: Customer
        }
      ]
    });

    if (!salesOrder) {
      return res.status(404).json({ error: 'Completed sales order not found' });
    }

    // Validate items
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ error: 'Items are required' });
    }

    // Create GRN request
    const grnRequest = await GRNReturnPDDRequest.create({
      organization_id: organizationId,
      sales_order_id,
      request_type: request_type || 'both',
      requested_by: null, // TODO: Get from authenticated user
      requested_at: new Date(),
      notes
    }, { transaction });

    // Process items immediately
    let totalPDDAmount = 0;
    
    for (const item of items) {
      if (!item.sales_order_item_id) {
        throw new Error('sales_order_item_id is required for each item');
      }

      // Find the corresponding sales order item
      const salesOrderItem = salesOrder.items?.find(soi => soi.id === item.sales_order_item_id);
      if (!salesOrderItem) {
        throw new Error(`Sales order item ${item.sales_order_item_id} not found`);
      }

      // Validation: Prevent PDD when returning full quantity
      const returnQuantity = item.return_quantity || 0;
      const originalQuantity = item.original_quantity || salesOrderItem.quantity;
      const pddAmount = item.pdd_amount || 0;
      const pddPercentage = item.pdd_percentage || 0;

      if (request_type === 'both' && 
          returnQuantity >= originalQuantity && 
          (pddAmount > 0 || pddPercentage > 0)) {
        throw new Error(`PDD discount cannot be applied when returning the full quantity (${returnQuantity}/${originalQuantity}) for product: ${salesOrderItem.product_name} (${salesOrderItem.sku_code})`);
      }

      // Create GRN item
      await GRNReturnPDDItem.create({
        organization_id: organizationId,
        grn_request_id: grnRequest.id,
        sales_order_item_id: item.sales_order_item_id,
        original_quantity: originalQuantity,
        return_quantity: returnQuantity,
        pdd_percentage: pddPercentage,
        pdd_amount: pddAmount
      }, { transaction });

      // Process returns - create inventory transactions
      if (returnQuantity > 0) {
        await InventoryTransactionService.createGRNReturnTransaction(
          organizationId,
          salesOrderItem.product_id,
          salesOrderItem.sku_id,
          returnQuantity,
          grnRequest.id,
          undefined, // TODO: Get from authenticated user
          `Return from sales order ${sales_order_id}`,
          transaction
        );

        // Update sales order item grn_quantity
        const newGrnQuantity = Math.max(0, originalQuantity - returnQuantity);
        const newTotalPrice = newGrnQuantity * salesOrderItem.unit_price;
        
        await salesOrderItem.update({
          grn_quantity: newGrnQuantity,
          total_price: newTotalPrice
        }, { transaction });
      }

      // Calculate PDD amount
      if (pddAmount > 0) {
        totalPDDAmount += Number(pddAmount);
      }
    }

    // Recalculate sales order totals
    const updatedItems = await SalesOrderItem.findAll({
      where: { sales_order_id },
      transaction
    });

    const newSubtotal = updatedItems.reduce((sum, item) => sum + Number(item.total_price), 0);
    const currentTax = Number(salesOrder.tax_amount || 0);
    const currentDiscount = Number(salesOrder.discount_amount || 0);
    const newTotalAmount = newSubtotal + currentTax - (currentDiscount + totalPDDAmount);

    // Calculate total return quantity for PDD discount reason
    const totalReturnQuantity = items.reduce((sum, item) => sum + (item.return_quantity || 0), 0);

    // Update sales order
    const updateData: any = {
      subtotal: newSubtotal,
      total_amount: newTotalAmount,
      status: 'grn_complete'
    };

    if (totalPDDAmount > 0) {
      updateData.discount_amount = currentDiscount + totalPDDAmount;
      updateData.discount_source = 'pdd';
      updateData.discount_reason = `PDD applied: ₹${totalPDDAmount.toFixed(2)} discount${totalReturnQuantity > 0 ? ` with ${totalReturnQuantity} items returned` : ''}`;
    }

    await salesOrder.update(updateData, { transaction });

    // Adjust customer balance for PDD amount
    if (totalPDDAmount > 0 && salesOrder.customer) {
      await Customer.decrement('current_balance', {
        by: totalPDDAmount,
        where: { 
          id: salesOrder.customer.id,
          organization_id: organizationId 
        },
        transaction
      });
    }

    await transaction.commit();
    
    res.status(201).json({ 
      message: 'Return/PDD request processed successfully',
      id: grnRequest.id,
      totalPDDAmount,
      newOrderTotal: newTotalAmount
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating return/PDD request:', error);
    res.status(500).json({ 
      error: 'Failed to create return/PDD request',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};


// Get GRN request history for a sales order
export const getGRNHistory = async (req: Request, res: Response) => {
  try {
    const { salesOrderId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    const grnHistory = await GRNReturnPDDRequest.findAll({
      where: { 
        sales_order_id: salesOrderId,
        organization_id: organizationId 
      },
      include: [
        {
          model: GRNReturnPDDItem,
          as: 'items',
          include: [{ model: SalesOrderItem }]
        }
      ],
      order: [['requested_at', 'DESC']]
    });

    res.json(grnHistory);
  } catch (error) {
    console.error('Error fetching GRN history:', error);
    res.status(500).json({ error: 'Failed to fetch GRN history' });
  }
};

// Get inventory transactions for a specific GRN request
export const getGRNInventoryTransactions = async (req: Request, res: Response) => {
  try {
    const { grnRequestId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    const transactions = await InventoryTransactionService.getTransactionsByReference(
      organizationId,
      'grn_return',
      grnRequestId
    );

    res.json(transactions);
  } catch (error) {
    console.error('Error fetching GRN inventory transactions:', error);
    res.status(500).json({ error: 'Failed to fetch inventory transactions' });
  }
};

// Get inventory transaction history for a product/SKU
export const getProductInventoryTransactions = async (req: Request, res: Response) => {
  try {
    const { productId, skuId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    const { limit = 50, offset = 0 } = req.query;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    const result = await InventoryTransactionService.getInventoryTransactionHistory(
      organizationId,
      productId,
      skuId,
      Number(limit),
      Number(offset)
    );

    res.json(result);
  } catch (error) {
    console.error('Error fetching product inventory transactions:', error);
    res.status(500).json({ error: 'Failed to fetch inventory transactions' });
  }
};
