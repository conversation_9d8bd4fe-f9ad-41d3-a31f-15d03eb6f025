import { Request, Response } from 'express';
import { Transaction } from 'sequelize';
import sequelize from '../config/sequelize';
import { 
  SalesOrder, 
  SalesOrderItem, 
  GRNReturnPDDRequest, 
  GRNReturnPDDItem,
  Customer,
  CurrentInventory
} from '../models';
import { InventoryTransactionService } from '../services/inventoryTransactionService';

// Mark sales order as GRN Complete
export const markGRNComplete = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const salesOrder = await SalesOrder.findOne({
      where: { 
        id, 
        organization_id: organizationId, 
        status: 'completed' 
      }
    });

    if (!salesOrder) {
      return res.status(404).json({ error: 'Completed sales order not found' });
    }

    await salesOrder.update({ status: 'grn_complete' });
    
    res.json({ 
      message: 'Order marked as GRN Complete successfully',
      order: salesOrder
    });
  } catch (error) {
    console.error('Error marking GRN complete:', error);
    res.status(500).json({ error: 'Failed to mark GRN complete' });
  }
};

// Create Return/PDD request
export const createReturnPDDRequest = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    const { sales_order_id, request_type, items, notes } = req.body;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Validate sales order is in completed status
    const salesOrder = await SalesOrder.findOne({
      where: { 
        id: sales_order_id, 
        organization_id: organizationId, 
        status: 'completed' 
      },
      include: [
        {
          model: SalesOrderItem,
          as: 'items'
        }
      ]
    });

    if (!salesOrder) {
      return res.status(404).json({ error: 'Completed sales order not found' });
    }

    // Validate items
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ error: 'Items are required' });
    }

    // Create GRN request
    const grnRequest = await GRNReturnPDDRequest.create({
      organization_id: organizationId,
      sales_order_id,
      request_type: request_type || 'both',
      requested_by: null, // TODO: Get from authenticated user
      requested_at: new Date(),
      approval_status: 'pending',
      notes
    }, { transaction });

    // Create GRN items and update inventory for returns immediately
    for (const item of items) {
      if (!item.sales_order_item_id) {
        throw new Error('sales_order_item_id is required for each item');
      }

      // Find the corresponding sales order item to get product details
      const salesOrderItem = salesOrder.items?.find(soi => soi.id === item.sales_order_item_id);
      if (!salesOrderItem) {
        throw new Error(`Sales order item ${item.sales_order_item_id} not found`);
      }

      await GRNReturnPDDItem.create({
        organization_id: organizationId,
        grn_request_id: grnRequest.id,
        sales_order_item_id: item.sales_order_item_id,
        original_quantity: item.original_quantity || 0,
        return_quantity: item.return_quantity || 0,
        pdd_percentage: item.pdd_percentage || 0,
        pdd_amount: item.pdd_amount || 0
      }, { transaction });

      // Create inventory transaction for returns
      if (item.return_quantity > 0) {
        await InventoryTransactionService.createGRNReturnTransaction(
          organizationId,
          salesOrderItem.product_id,
          salesOrderItem.sku_id,
          item.return_quantity,
          grnRequest.id,
          undefined, // TODO: Get from authenticated user
          `Return from sales order ${sales_order_id}`,
          transaction
        );

        // Update sales order item quantity and total price to reflect the return
        const newQuantity = Math.max(0, salesOrderItem.quantity - item.return_quantity);
        const newTotalPrice = newQuantity * salesOrderItem.unit_price;
        
        await salesOrderItem.update({
          quantity: newQuantity,
          total_price: newTotalPrice
        }, { transaction });
      }
    }

    // Recalculate sales order totals after item updates
    const updatedItems = await SalesOrderItem.findAll({
      where: { sales_order_id: sales_order_id },
      transaction
    });

    const newSubtotal = updatedItems.reduce((sum, item) => sum + Number(item.total_price), 0);
    const currentDiscount = Number(salesOrder.discount_amount || 0);
    const currentTax = Number(salesOrder.tax_amount || 0);
    const newTotal = newSubtotal + currentTax - currentDiscount;

    await salesOrder.update({
      subtotal: newSubtotal,
      total_amount: newTotal,
      status: 'grn_pending_approval'
    }, { transaction });

    await transaction.commit();
    
    res.status(201).json({ 
      message: 'Return/PDD request created successfully', 
      id: grnRequest.id 
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating return/PDD request:', error);
    res.status(500).json({ 
      error: 'Failed to create return/PDD request',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get pending GRN approvals
export const getPendingGRNApprovals = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    const pendingRequests = await GRNReturnPDDRequest.findAll({
      where: { 
        organization_id: organizationId, 
        approval_status: 'pending' 
      },
      include: [
        {
          model: SalesOrder,
          include: [
            { model: Customer },
            { model: SalesOrderItem, as: 'items' }
          ]
        },
        {
          model: GRNReturnPDDItem,
          as: 'items',
          include: [{ model: SalesOrderItem }]
        }
      ],
      order: [['requested_at', 'DESC']]
    });

    res.json(pendingRequests);
  } catch (error) {
    console.error('Error fetching pending GRN approvals:', error);
    res.status(500).json({ error: 'Failed to fetch pending approvals' });
  }
};

// Approve GRN request
export const approveGRNRequest = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    const grnRequest = await GRNReturnPDDRequest.findOne({
      where: { 
        id, 
        organization_id: organizationId, 
        approval_status: 'pending' 
      },
      include: [
        { 
          model: SalesOrder,
          include: [{ model: Customer }]
        },
        { 
          model: GRNReturnPDDItem,
          as: 'items',
          include: [{ model: SalesOrderItem }] 
        }
      ]
    });

    if (!grnRequest) {
      return res.status(404).json({ error: 'Pending GRN request not found' });
    }

    // Update GRN request status
    await grnRequest.update({
      approval_status: 'approved',
      approved_by: null, // TODO: Get from authenticated user
      approved_at: new Date()
    }, { transaction });

    // Process PDD adjustments (returns are already handled when request was created)
    let totalPDDAmount = 0;
    
    for (const item of grnRequest.items) {
      // Calculate PDD amount
      if (item.pdd_amount > 0) {
        totalPDDAmount += Number(item.pdd_amount);
      }
    }

    // Update sales order total if PDD applied
    if (totalPDDAmount > 0) {
      const currentTotal = Number(grnRequest.sales_order.total_amount || 0);
      const newTotal = currentTotal - totalPDDAmount;
      const currentDiscount = Number(grnRequest.sales_order.discount_amount || 0);
      
      await grnRequest.sales_order.update({
        total_amount: newTotal,
        discount_amount: currentDiscount + totalPDDAmount
      }, { transaction });

      // Adjust customer balance
      await Customer.decrement('current_balance', {
        by: totalPDDAmount,
        where: { 
          id: grnRequest.sales_order.customer_id,
          organization_id: organizationId 
        },
        transaction
      });
    }

    // Update sales order status to final status
    await grnRequest.sales_order.update({ 
      status: 'grn_complete' 
    }, { transaction });

    await transaction.commit();
    
    res.json({ 
      message: 'GRN request approved successfully',
      totalPDDAmount,
      newOrderTotal: grnRequest.sales_order.total_amount
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error approving GRN request:', error);
    res.status(500).json({ error: 'Failed to approve GRN request' });
  }
};

// Reject GRN request
export const rejectGRNRequest = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const { rejection_reason } = req.body;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    if (!rejection_reason || !rejection_reason.trim()) {
      return res.status(400).json({ error: 'Rejection reason is required' });
    }
    
    const grnRequest = await GRNReturnPDDRequest.findOne({
      where: { 
        id, 
        organization_id: organizationId, 
        approval_status: 'pending' 
      },
      include: [
        { model: SalesOrder },
        { 
          model: GRNReturnPDDItem,
          as: 'items',
          include: [{ model: SalesOrderItem }] 
        }
      ]
    });

    if (!grnRequest) {
      return res.status(404).json({ error: 'Pending GRN request not found' });
    }

    // Revert inventory changes for returns by creating negative adjustment transactions
    // and restore sales order item quantities
    for (const item of grnRequest.items) {
      if (item.return_quantity > 0) {
        await InventoryTransactionService.createAdjustmentTransaction(
          organizationId,
          item.sales_order_item.product_id,
          item.sales_order_item.sku_id,
          -item.return_quantity, // Negative to revert the return
          `GRN Return Rejected - Reverting return for GRN request ${id}`,
          undefined, // TODO: Get from authenticated user
          `Rejection reason: ${rejection_reason.trim()}`,
          transaction
        );

        // Restore sales order item quantity and total price back to original
        const restoredQuantity = item.sales_order_item.quantity + item.return_quantity;
        const restoredTotalPrice = restoredQuantity * item.sales_order_item.unit_price;
        
        await item.sales_order_item.update({
          quantity: restoredQuantity,
          total_price: restoredTotalPrice
        }, { transaction });
      }
    }

    // Update GRN request status
    await grnRequest.update({
      approval_status: 'rejected',
      approved_by: null, // TODO: Get from authenticated user
      approved_at: new Date(),
      rejection_reason: rejection_reason.trim()
    }, { transaction });

    // Recalculate sales order totals after restoring item quantities
    const restoredItems = await SalesOrderItem.findAll({
      where: { sales_order_id: grnRequest.sales_order_id },
      transaction
    });

    const restoredSubtotal = restoredItems.reduce((sum, item) => sum + Number(item.total_price), 0);
    const currentDiscount = Number(grnRequest.sales_order.discount_amount || 0);
    const currentTax = Number(grnRequest.sales_order.tax_amount || 0);
    const restoredTotal = restoredSubtotal + currentTax - currentDiscount;

    // Revert sales order status back to completed and update totals
    await grnRequest.sales_order.update({ 
      subtotal: restoredSubtotal,
      total_amount: restoredTotal,
      status: 'completed' 
    }, { transaction });

    await transaction.commit();

    res.json({ 
      message: 'GRN request rejected successfully',
      reason: rejection_reason.trim()
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error rejecting GRN request:', error);
    res.status(500).json({ error: 'Failed to reject GRN request' });
  }
};

// Get GRN request history for a sales order
export const getGRNHistory = async (req: Request, res: Response) => {
  try {
    const { salesOrderId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    const grnHistory = await GRNReturnPDDRequest.findAll({
      where: { 
        sales_order_id: salesOrderId,
        organization_id: organizationId 
      },
      include: [
        {
          model: GRNReturnPDDItem,
          as: 'items',
          include: [{ model: SalesOrderItem }]
        }
      ],
      order: [['requested_at', 'DESC']]
    });

    res.json(grnHistory);
  } catch (error) {
    console.error('Error fetching GRN history:', error);
    res.status(500).json({ error: 'Failed to fetch GRN history' });
  }
};

// Get inventory transactions for a specific GRN request
export const getGRNInventoryTransactions = async (req: Request, res: Response) => {
  try {
    const { grnRequestId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    const transactions = await InventoryTransactionService.getTransactionsByReference(
      organizationId,
      'grn_return',
      grnRequestId
    );

    res.json(transactions);
  } catch (error) {
    console.error('Error fetching GRN inventory transactions:', error);
    res.status(500).json({ error: 'Failed to fetch inventory transactions' });
  }
};

// Get inventory transaction history for a product/SKU
export const getProductInventoryTransactions = async (req: Request, res: Response) => {
  try {
    const { productId, skuId } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    const { limit = 50, offset = 0 } = req.query;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }
    
    const result = await InventoryTransactionService.getInventoryTransactionHistory(
      organizationId,
      productId,
      skuId,
      Number(limit),
      Number(offset)
    );

    res.json(result);
  } catch (error) {
    console.error('Error fetching product inventory transactions:', error);
    res.status(500).json({ error: 'Failed to fetch inventory transactions' });
  }
};
