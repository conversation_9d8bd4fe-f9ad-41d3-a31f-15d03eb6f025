import { Request, Response } from 'express';
import { Transaction } from 'sequelize';
import sequelize from '../config/sequelize';
import { VehicleArrival, VehicleArrivalItem, VehicleArrivalAttachment } from '../models/VehicleArrival';
import { PurchaseRecord } from '../models/PurchaseRecord';
import { Organization } from '../models/Organization';
import { Product, SKU } from '../models/Product';
import { CurrentInventory } from '../models/Inventory';

// Get all vehicle arrivals
export const getVehicleArrivals = async (req: Request, res: Response) => {
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const vehicleArrivals = await VehicleArrival.findAll({
      where: { organization_id: organizationId },
      include: [
        {
          model: VehicleArrivalItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            },
            {
              model: SKU,
              as: 'sku'
            }
          ]
        },
        {
          model: VehicleArrivalAttachment,
          as: 'attachments'
        },
        {
          model: PurchaseRecord,
          as: 'purchase_records'
        }
      ],
      order: [['arrival_time', 'DESC']]
    });

    res.json(vehicleArrivals);
  } catch (error) {
    console.error('Error fetching vehicle arrivals:', error);
    res.status(500).json({ error: 'Failed to fetch vehicle arrivals' });
  }
};

// Get single vehicle arrival
export const getVehicleArrival = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const vehicleArrival = await VehicleArrival.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: VehicleArrivalItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            },
            {
              model: SKU,
              as: 'sku'
            }
          ]
        },
        {
          model: VehicleArrivalAttachment,
          as: 'attachments'
        },
        {
          model: PurchaseRecord,
          as: 'purchase_records'
        }
      ]
    });
    
    if (!vehicleArrival) {
      return res.status(404).json({ error: 'Vehicle arrival not found' });
    }

    res.json(vehicleArrival);
  } catch (error) {
    console.error('Error fetching vehicle arrival:', error);
    res.status(500).json({ error: 'Failed to fetch vehicle arrival' });
  }
};

// Create vehicle arrival
export const createVehicleArrival = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const {
      vehicle_number,
      supplier,
      driver_name,
      driver_contact,
      arrival_time,
      source_location,
      notes,
      status = 'pending',
      items = [],
      attachments = []
    } = req.body;

    // Validate required fields
    if (!supplier || !arrival_time) {
      return res.status(400).json({ 
        error: 'Supplier and arrival time are required' 
      });
    }

    // Create vehicle arrival
    const vehicleArrival = await VehicleArrival.create({
      organization_id: organizationId,
      vehicle_number,
      supplier,
      driver_name,
      driver_contact,
      arrival_time,
      source_location,
      notes,
      status
    }, { transaction });

    // Create vehicle arrival items
    if (items.length > 0) {
      const itemsData = items.map((item: any) => ({
        organization_id: organizationId,
        vehicle_arrival_id: vehicleArrival.id,
        product_id: item.product_id,
        sku_id: item.sku_id,
        unit_type: item.unit_type || 'box',
        unit_weight: item.unit_weight || null,
        quantity: item.quantity,
        total_weight: item.total_weight,
        final_quantity: item.final_quantity || null,
        final_total_weight: item.final_total_weight || null
      }));

      await VehicleArrivalItem.bulkCreate(itemsData, { transaction });
    }

    // Create vehicle arrival attachments
    if (attachments.length > 0) {
      const attachmentsData = attachments.map((attachment: any) => ({
        organization_id: organizationId,
        vehicle_arrival_id: vehicleArrival.id,
        file_name: attachment.file_name,
        file_type: attachment.file_type,
        file_size: attachment.file_size,
        file_url: attachment.file_url
      }));

      await VehicleArrivalAttachment.bulkCreate(attachmentsData, { transaction });
    }

    // Update inventory if status is completed
    if (status === 'completed' && items.length > 0) {
      for (const item of items) {
        const quantity = item.final_quantity || item.quantity;
        const totalWeight = item.final_total_weight || item.total_weight;
        
        if (quantity > 0) {
          // Get product and SKU details for inventory
          const product = await Product.findByPk(item.product_id, { transaction });
          const sku = await SKU.findByPk(item.sku_id, { transaction });
          
          // Check if inventory record already exists
          const existingInventory = await CurrentInventory.findOne({
            where: {
              organization_id: organizationId,
              product_id: item.product_id,
              sku_id: item.sku_id
            },
            transaction
          });

          if (existingInventory) {
            // Update existing inventory by adding the quantities
            await existingInventory.update({
              available_quantity: existingInventory.available_quantity + quantity,
              total_weight: existingInventory.total_weight + (totalWeight || 0),
              last_updated_at: new Date()
            }, { transaction });
          } else {
            // Create new inventory record
            await CurrentInventory.create({
              organization_id: organizationId,
              product_id: item.product_id,
              sku_id: item.sku_id,
              product_name: product?.name || 'Unknown Product',
              sku_code: sku?.code || 'Unknown SKU',
              unit_type: item.unit_type || 'box',
              available_quantity: quantity,
              total_weight: totalWeight || 0,
              last_updated_at: new Date()
            }, { transaction });
          }
        }
      }
    }

    await transaction.commit();

    // Fetch the created vehicle arrival with associations
    const createdVehicleArrival = await VehicleArrival.findByPk(vehicleArrival.id, {
      include: [
        {
          model: VehicleArrivalItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            },
            {
              model: SKU,
              as: 'sku'
            }
          ]
        },
        {
          model: VehicleArrivalAttachment,
          as: 'attachments'
        }
      ]
    });
    
    res.status(201).json(createdVehicleArrival);
  } catch (error) {
    await transaction.rollback();
    console.error('Error creating vehicle arrival:', error);
    res.status(500).json({ error: 'Failed to create vehicle arrival' });
  }
};

// Update vehicle arrival
export const updateVehicleArrival = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const {
      vehicle_number,
      supplier,
      driver_name,
      driver_contact,
      arrival_time,
      source_location,
      notes,
      status,
      items,
      attachments
    } = req.body;

    // Find vehicle arrival
    const vehicleArrival = await VehicleArrival.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!vehicleArrival) {
      return res.status(404).json({ error: 'Vehicle arrival not found' });
    }

    // Update vehicle arrival
    const updateData: any = {};
    if (vehicle_number !== undefined) updateData.vehicle_number = vehicle_number;
    if (supplier !== undefined) updateData.supplier = supplier;
    if (driver_name !== undefined) updateData.driver_name = driver_name;
    if (driver_contact !== undefined) updateData.driver_contact = driver_contact;
    if (arrival_time !== undefined) updateData.arrival_time = arrival_time;
    if (source_location !== undefined) updateData.source_location = source_location;
    if (notes !== undefined) updateData.notes = notes;
    if (status !== undefined) updateData.status = status;

    await vehicleArrival.update(updateData, { transaction });

    // Update items if provided
    if (items !== undefined) {
      // Delete existing items
      await VehicleArrivalItem.destroy({
        where: { vehicle_arrival_id: id },
        transaction
      });

      // Create new items
      if (items.length > 0) {
        const itemsData = items.map((item: any) => ({
          organization_id: organizationId,
          vehicle_arrival_id: id,
          product_id: item.product_id,
          sku_id: item.sku_id,
          unit_type: item.unit_type || 'box',
          unit_weight: item.unit_weight || null,
          quantity: item.quantity,
          total_weight: item.total_weight,
          final_quantity: item.final_quantity || null,
          final_total_weight: item.final_total_weight || null
        }));

        await VehicleArrivalItem.bulkCreate(itemsData, { transaction });
      }
    }

    // Update attachments if provided
    if (attachments !== undefined) {
      // Delete existing attachments
      await VehicleArrivalAttachment.destroy({
        where: { vehicle_arrival_id: id },
        transaction
      });

      // Create new attachments
      if (attachments.length > 0) {
        const attachmentsData = attachments.map((attachment: any) => ({
          organization_id: organizationId,
          vehicle_arrival_id: id,
          file_name: attachment.file_name,
          file_type: attachment.file_type,
          file_size: attachment.file_size,
          file_url: attachment.file_url
        }));

        await VehicleArrivalAttachment.bulkCreate(attachmentsData, { transaction });
      }
    }

    // Update inventory if status is being changed to completed
    if (status === 'completed' && vehicleArrival.status !== 'completed') {
      // Get the current items for this vehicle arrival
      const currentItems = await VehicleArrivalItem.findAll({
        where: { vehicle_arrival_id: id },
        include: [
          {
            model: Product,
            as: 'product'
          },
          {
            model: SKU,
            as: 'sku'
          }
        ],
        transaction
      });

      for (const item of currentItems) {
        const quantity = item.final_quantity || item.quantity;
        const totalWeight = item.final_total_weight || item.total_weight;
        
        if (quantity > 0) {
          // Check if inventory record already exists
          const existingInventory = await CurrentInventory.findOne({
            where: {
              organization_id: organizationId,
              product_id: item.product_id,
              sku_id: item.sku_id
            },
            transaction
          });

          if (existingInventory) {
            // Update existing inventory by adding the quantities
            await existingInventory.update({
              available_quantity: existingInventory.available_quantity + quantity,
              total_weight: existingInventory.total_weight + (totalWeight || 0),
              last_updated_at: new Date()
            }, { transaction });
          } else {
            // Create new inventory record
            await CurrentInventory.create({
              organization_id: organizationId,
              product_id: item.product_id,
              sku_id: item.sku_id,
              product_name: item.product?.name || 'Unknown Product',
              sku_code: item.sku?.code || 'Unknown SKU',
              unit_type: item.unit_type,
              available_quantity: quantity,
              total_weight: totalWeight || 0,
              last_updated_at: new Date()
            }, { transaction });
          }
        }
      }
    }

    await transaction.commit();

    // Fetch the updated vehicle arrival
    const updatedVehicleArrival = await VehicleArrival.findByPk(id, {
      include: [
        {
          model: VehicleArrivalItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            },
            {
              model: SKU,
              as: 'sku'
            }
          ]
        },
        {
          model: VehicleArrivalAttachment,
          as: 'attachments'
        }
      ]
    });
    
    res.json(updatedVehicleArrival);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating vehicle arrival:', error);
    res.status(500).json({ error: 'Failed to update vehicle arrival' });
  }
};

// Update vehicle arrival status with final quantities
export const updateVehicleArrivalStatus = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    const { status, notes, final_quantities = [] } = req.body;

    // Find vehicle arrival
    const vehicleArrival = await VehicleArrival.findOne({
      where: { 
        id,
        organization_id: organizationId 
      },
      include: [
        {
          model: VehicleArrivalItem,
          as: 'items'
        }
      ]
    });

    if (!vehicleArrival) {
      return res.status(404).json({ error: 'Vehicle arrival not found' });
    }

    // Update vehicle arrival status
    await vehicleArrival.update({ 
      status, 
      notes 
    }, { transaction });

    // Update final quantities if status is completed
    if (status === 'completed' && final_quantities.length > 0) {
      for (const item of final_quantities) {
        await VehicleArrivalItem.update(
          {
            final_quantity: item.final_quantity,
            final_total_weight: item.final_total_weight
          },
          {
            where: { 
              id: item.item_id,
              vehicle_arrival_id: id 
            },
            transaction
          }
        );

        // Update current inventory based on final quantities received
        const vehicleArrivalItem = await VehicleArrivalItem.findOne({
          where: { id: item.item_id },
          include: [
            {
              model: Product,
              as: 'product'
            },
            {
              model: SKU,
              as: 'sku'
            }
          ],
          transaction
        });

        if (vehicleArrivalItem && item.final_quantity > 0) {
          // Check if inventory record already exists
          const existingInventory = await CurrentInventory.findOne({
            where: {
              organization_id: organizationId,
              product_id: vehicleArrivalItem.product_id,
              sku_id: vehicleArrivalItem.sku_id
            },
            transaction
          });

          if (existingInventory) {
            // Update existing inventory by adding the final quantities
            await existingInventory.update({
              available_quantity: existingInventory.available_quantity + item.final_quantity,
              total_weight: existingInventory.total_weight + (item.final_total_weight || 0),
              last_updated_at: new Date()
            }, { transaction });
          } else {
            // Create new inventory record using the same pattern as createInventoryItem
            await CurrentInventory.create({
              organization_id: organizationId,
              product_id: vehicleArrivalItem.product_id,
              sku_id: vehicleArrivalItem.sku_id,
              product_name: vehicleArrivalItem.product?.name || 'Unknown Product',
              sku_code: vehicleArrivalItem.sku?.code || 'Unknown SKU',
              unit_type: vehicleArrivalItem.unit_type,
              available_quantity: item.final_quantity,
              total_weight: item.final_total_weight || 0,
              last_updated_at: new Date()
            }, { transaction });
          }
        }
      }
    }

    await transaction.commit();

    // Fetch the updated vehicle arrival
    const updatedVehicleArrival = await VehicleArrival.findByPk(id, {
      include: [
        {
          model: VehicleArrivalItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product'
            },
            {
              model: SKU,
              as: 'sku'
            }
          ]
        },
        {
          model: VehicleArrivalAttachment,
          as: 'attachments'
        }
      ]
    });
    
    res.json(updatedVehicleArrival);
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating vehicle arrival status:', error);
    res.status(500).json({ error: 'Failed to update vehicle arrival status' });
  }
};

// Delete vehicle arrival
export const deleteVehicleArrival = async (req: Request, res: Response) => {
  const transaction: Transaction = await sequelize.transaction();
  
  try {
    const { id } = req.params;
    const organizationId = req.headers['x-organization-id'] as string;
    
    if (!organizationId) {
      return res.status(400).json({ error: 'Organization ID is required' });
    }

    // Find vehicle arrival
    const vehicleArrival = await VehicleArrival.findOne({
      where: { 
        id,
        organization_id: organizationId 
      }
    });

    if (!vehicleArrival) {
      return res.status(404).json({ error: 'Vehicle arrival not found' });
    }

    // Delete related records (cascade should handle this, but being explicit)
    await VehicleArrivalAttachment.destroy({
      where: { vehicle_arrival_id: id },
      transaction
    });

    await VehicleArrivalItem.destroy({
      where: { vehicle_arrival_id: id },
      transaction
    });

    await vehicleArrival.destroy({ transaction });

    await transaction.commit();
    
    res.json({ message: 'Vehicle arrival deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    console.error('Error deleting vehicle arrival:', error);
    res.status(500).json({ error: 'Failed to delete vehicle arrival' });
  }
};
