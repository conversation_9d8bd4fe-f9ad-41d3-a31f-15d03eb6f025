import { Request, Response, NextFunction } from 'express';
import { User } from '../models/User';
import { UserRole } from '../models/UserRole';
import { Role } from '../models/Role';
import { Page } from '../models/Page';
import { UserPagePermission } from '../models/UserPagePermission';

interface AuthenticatedRequest extends Request {
  user?: User;
  userId?: string;
  organizationId?: string;
  userPermissions?: string[];
  userRoles?: string[];
  userPages?: string[];
}

/**
 * Get user permissions for the current organization
 */
export const getUserPermissions = async (userId: string, organizationId: string): Promise<string[]> => {
  try {
    // Get user roles for the organization
    const userRoles = await UserRole.findAll({
      where: {
        user_id: userId,
        organization_id: organizationId,
        status: 'active'
      },
      include: [
        {
          model: Role,
          where: { status: 'active' }
        }
      ]
    });

    // Collect all permissions from roles
    const permissions = new Set<string>();
    
    userRoles.forEach(userRole => {
      if (userRole.role && userRole.role.permissions) {
        userRole.role.permissions.forEach(permission => {
          permissions.add(permission);
        });
      }
    });

    return Array.from(permissions);
  } catch (error) {
    console.error('Error getting user permissions:', error);
    return [];
  }
};

/**
 * Get user roles for the current organization
 */
export const getUserRoles = async (userId: string, organizationId: string): Promise<string[]> => {
  try {
    const userRoles = await UserRole.findAll({
      where: {
        user_id: userId,
        organization_id: organizationId,
        status: 'active'
      },
      include: [
        {
          model: Role,
          where: { status: 'active' }
        }
      ]
    });

    return userRoles.map(userRole => userRole.role.name);
  } catch (error) {
    console.error('Error getting user roles:', error);
    return [];
  }
};

/**
 * Get pages user can access
 */
export const getUserPages = async (userId: string, organizationId: string): Promise<string[]> => {
  try {
    // Get permissions from roles
    const permissions = await getUserPermissions(userId, organizationId);
    
    // Get user roles
    const userRoles = await UserRole.findAll({
      where: {
        user_id: userId,
        organization_id: organizationId,
        status: 'active'
      },
      include: [
        {
          model: Role,
          where: { status: 'active' }
        }
      ]
    });

    // Collect pages from roles
    const pages = new Set<string>();
    
    userRoles.forEach(userRole => {
      if (userRole.role && userRole.role.pages) {
        userRole.role.pages.forEach(pageId => {
          pages.add(pageId);
        });
      }
    });

    // Also get direct page permissions
    const directPagePermissions = await UserPagePermission.findAll({
      where: {
        user_id: userId,
        can_view: true
      },
      include: [Page]
    });

    directPagePermissions.forEach(permission => {
      if (permission.page) {
        pages.add(permission.page.name);
      }
    });

    return Array.from(pages);
  } catch (error) {
    console.error('Error getting user pages:', error);
    return [];
  }
};

/**
 * Middleware to load user permissions
 */
export const loadUserPermissions = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    if (!req.userId || !req.organizationId) {
      return next();
    }

    // Load user permissions and roles
    const [permissions, roles, pages] = await Promise.all([
      getUserPermissions(req.userId, req.organizationId),
      getUserRoles(req.userId, req.organizationId),
      getUserPages(req.userId, req.organizationId)
    ]);

    req.userPermissions = permissions;
    req.userRoles = roles;
    req.userPages = pages;

    next();
  } catch (error) {
    console.error('Error loading user permissions:', error);
    next();
  }
};

/**
 * Check if user has specific permission
 */
export const hasPermission = (req: AuthenticatedRequest, permission: string): boolean => {
  if (!req.userPermissions) {
    return false;
  }
  return req.userPermissions.includes(permission);
};

/**
 * Check if user has any of the specified permissions
 */
export const hasAnyPermission = (req: AuthenticatedRequest, permissions: string[]): boolean => {
  if (!req.userPermissions) {
    return false;
  }
  return permissions.some(permission => req.userPermissions!.includes(permission));
};

/**
 * Check if user has all specified permissions
 */
export const hasAllPermissions = (req: AuthenticatedRequest, permissions: string[]): boolean => {
  if (!req.userPermissions) {
    return false;
  }
  return permissions.every(permission => req.userPermissions!.includes(permission));
};

/**
 * Check if user has specific role
 */
export const hasRole = (req: AuthenticatedRequest, role: string): boolean => {
  if (!req.userRoles) {
    return false;
  }
  return req.userRoles.includes(role);
};

/**
 * Check if user has any of the specified roles
 */
export const hasAnyRole = (req: AuthenticatedRequest, roles: string[]): boolean => {
  if (!req.userRoles) {
    return false;
  }
  return roles.some(role => req.userRoles!.includes(role));
};

/**
 * Check if user can access specific page
 */
export const canAccessPage = (req: AuthenticatedRequest, pageName: string): boolean => {
  if (!req.userPages) {
    return false;
  }
  return req.userPages.includes(pageName);
};

/**
 * Middleware factory to require specific permission
 */
export const requirePermission = (permission: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!hasPermission(req, permission)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required permission: ${permission}`,
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }
    next();
  };
};

/**
 * Middleware factory to require any of the specified permissions
 */
export const requireAnyPermission = (permissions: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!hasAnyPermission(req, permissions)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required permissions: ${permissions.join(' OR ')}`,
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }
    next();
  };
};

/**
 * Middleware factory to require all specified permissions
 */
export const requireAllPermissions = (permissions: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!hasAllPermissions(req, permissions)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required permissions: ${permissions.join(' AND ')}`,
        code: 'INSUFFICIENT_PERMISSIONS'
      });
    }
    next();
  };
};

/**
 * Middleware factory to require specific role
 */
export const requireRole = (role: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!hasRole(req, role)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required role: ${role}`,
        code: 'INSUFFICIENT_ROLE'
      });
    }
    next();
  };
};

/**
 * Middleware factory to require any of the specified roles
 */
export const requireAnyRole = (roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!hasAnyRole(req, roles)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Required roles: ${roles.join(' OR ')}`,
        code: 'INSUFFICIENT_ROLE'
      });
    }
    next();
  };
};

/**
 * Middleware to require page access
 */
export const requirePageAccess = (pageName: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!canAccessPage(req, pageName)) {
      return res.status(403).json({
        success: false,
        message: `Access denied. Cannot access page: ${pageName}`,
        code: 'PAGE_ACCESS_DENIED'
      });
    }
    next();
  };
};

/**
 * Admin only middleware
 */
export const requireAdmin = requireRole('admin');

/**
 * Manager or Admin middleware
 */
export const requireManagerOrAdmin = requireAnyRole(['admin', 'manager']);

// Export types for use in other files
export type { AuthenticatedRequest };
