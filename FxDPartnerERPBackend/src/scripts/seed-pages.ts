import sequelize from '../config/sequelize';
import { Page } from '../models/Page';

const pages = [
  // Main Dashboard
  {
    name: 'dashboard',
    display_name: 'Dashboard',
    category: 'MAIN',
    route_path: '/',
    icon: 'dashboard',
    sort_order: 1
  },
  
  // Procurement
  {
    name: 'vehicle_arrival',
    display_name: 'Vehicle Arrival',
    category: 'PROCUREMENT',
    route_path: '/vehicle-arrival',
    icon: 'truck',
    sort_order: 10
  },
  {
    name: 'record_purchase',
    display_name: 'Record Purchase',
    category: 'PROCUREMENT',
    route_path: '/record-purchase',
    icon: 'receipt',
    sort_order: 11
  },
  
  // Inventory
  {
    name: 'inventory',
    display_name: 'Inventory Management',
    category: 'INVENTORY',
    route_path: '/inventory',
    icon: 'warehouse',
    sort_order: 20
  },
  
  // Sales
  {
    name: 'sales',
    display_name: 'Sales Orders',
    category: 'SALES',
    route_path: '/sales',
    icon: 'shopping-cart',
    sort_order: 30
  },
  {
    name: 'dispatch',
    display_name: 'Dispatch',
    category: 'SALES',
    route_path: '/dispatch',
    icon: 'truck-loading',
    sort_order: 31
  },
  {
    name: 'pending_approvals',
    display_name: 'Pending Approvals',
    category: 'SALES',
    route_path: '/sales/pending-approvals',
    icon: 'clock',
    sort_order: 32
  },
  
  // Partners
  {
    name: 'customers',
    display_name: 'Customers',
    category: 'PARTNERS',
    route_path: '/customers',
    icon: 'users',
    sort_order: 40
  },
  {
    name: 'suppliers',
    display_name: 'Suppliers',
    category: 'PARTNERS',
    route_path: '/suppliers',
    icon: 'building',
    sort_order: 41
  },
  
  // Financials
  {
    name: 'ledger',
    display_name: 'Ledger',
    category: 'FINANCIALS',
    route_path: '/ledger',
    icon: 'book',
    sort_order: 50
  },
  {
    name: 'payments',
    display_name: 'Payments',
    category: 'FINANCIALS',
    route_path: '/payments',
    icon: 'credit-card',
    sort_order: 51
  },
  
  // System
  {
    name: 'settings',
    display_name: 'Settings',
    category: 'SYSTEM',
    route_path: '/settings',
    icon: 'cog',
    sort_order: 60
  }
];

async function seedPages() {
  try {
    console.log('🌱 Seeding pages...');
    
    // Connect to database
    await sequelize.authenticate();
    console.log('✅ Database connected successfully');
    
    // Clear existing pages
    await Page.destroy({ where: {} });
    console.log('🗑️  Cleared existing pages');
    
    // Insert new pages
    await Page.bulkCreate(pages);
    console.log(`✅ Created ${pages.length} pages`);
    
    console.log('🎉 Pages seeded successfully!');
  } catch (error) {
    console.error('❌ Error seeding pages:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the seeder
seedPages();
