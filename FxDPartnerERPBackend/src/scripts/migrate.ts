#!/usr/bin/env ts-node

import { runMigrations, getMigrationStatus, rollbackMigration } from '../utils/migration';
import { testConnection } from '../config/database';

const command = process.argv[2];
const version = process.argv[3];

async function main() {
  try {
    console.log('🔌 Testing database connection...');
    const connected = await testConnection();
    
    if (!connected) {
      console.error('❌ Database connection failed. Please check your configuration.');
      process.exit(1);
    }

    switch (command) {
      case 'up':
        console.log('🚀 Running migrations...');
        await runMigrations();
        break;
        
      case 'status':
        console.log('📊 Migration status:');
        const migrations = await getMigrationStatus();
        if (migrations.length === 0) {
          console.log('No migrations executed yet.');
        } else {
          migrations.forEach((migration: any) => {
            console.log(`✅ ${migration.version} - ${migration.executed_at}`);
          });
        }
        break;
        
      case 'rollback':
        if (!version) {
          console.error('❌ Please specify a migration version to rollback');
          console.log('Usage: npm run migrate rollback <version>');
          process.exit(1);
        }
        console.log(`🔄 Rolling back migration ${version}...`);
        await rollbackMigration(version);
        break;
        
      default:
        console.log('Usage:');
        console.log('  npm run migrate up       - Run all pending migrations');
        console.log('  npm run migrate status   - Show migration status');
        console.log('  npm run migrate rollback <version> - Rollback a migration');
        break;
    }
  } catch (error) {
    console.error('❌ Migration script failed:', error);
    process.exit(1);
  }
}

main();
