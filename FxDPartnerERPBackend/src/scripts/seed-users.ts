import bcrypt from 'bcrypt';
import { User } from '../models/User';
import { Organization } from '../models/Organization';
import { UserOrganization } from '../models/UserOrganization';
import { UserRole } from '../models/UserRole';
import { Role } from '../models/Role';
import sequelize from '../config/sequelize';

async function seedUsers() {
  try {
    await sequelize.authenticate();
    console.log('Database connected successfully');

    // Create default organization if it doesn't exist
    const [organization] = await Organization.findOrCreate({
      where: { code: 'FXD_FRUIT_SHOP' },
      defaults: {
        name: 'FXD Fruit Shop',
        code: 'FXD_FRUIT_SHOP',
        email: '<EMAIL>',
        phone: '+1234567890',
        address: '123 Fruit Street, Fresh City',
        status: 'active',
        subscription_plan: 'premium'
      }
    });

    console.log('Organization created/found:', organization.name);

    // Create default roles if they don't exist
    const [adminRole] = await Role.findOrCreate({
      where: { name: 'admin' },
      defaults: {
        name: 'admin',
        description: 'Administrator with full access',
        permissions: JSON.stringify(['*'])
      }
    });

    const [managerRole] = await Role.findOrCreate({
      where: { name: 'manager' },
      defaults: {
        name: 'manager',
        description: 'Manager with limited administrative access',
        permissions: JSON.stringify(['read', 'write', 'manage_inventory', 'manage_sales'])
      }
    });

    const [staffRole] = await Role.findOrCreate({
      where: { name: 'staff' },
      defaults: {
        name: 'staff',
        description: 'Staff member with basic access',
        permissions: JSON.stringify(['read', 'write'])
      }
    });

    console.log('Roles created/found');

    // Hash passwords
    const defaultHashedPassword = await bcrypt.hash('password', 10);
    const customHashedPassword = await bcrypt.hash('vedved123', 10);

    // Create test users
    const users = [
      {
        email: '<EMAIL>',
        first_name: 'Admin',
        phone: '+1234567890',
        role: adminRole,
        password: defaultHashedPassword
      },
      {
        email: '<EMAIL>',
        first_name: 'Manager',
        phone: '+1234567891',
        role: managerRole,
        password: defaultHashedPassword
      },
      {
        email: '<EMAIL>',
        first_name: 'Staff',
        phone: '+1234567892',
        role: staffRole,
        password: defaultHashedPassword
      },
      {
        email: '<EMAIL>',
        first_name: 'Demo',
        phone: '+1234567893',
        role: adminRole,
        password: defaultHashedPassword
      },
      {
        email: '<EMAIL>',
        first_name: 'Chaitanya',
        phone: '+1234567894',
        role: adminRole,
        password: customHashedPassword
      }
    ];

    for (const userData of users) {
      // Check if user already exists
      const existingUser = await User.findOne({ where: { email: userData.email } });
      
      if (existingUser) {
        console.log(`User ${userData.email} already exists, skipping...`);
        continue;
      }

      // Create user
      const user = await User.create({
        email: userData.email,
        password_hash: userData.password,
        first_name: userData.first_name,
        phone: userData.phone,
        organization_id: organization.id,
        status: 'active'
      });

      // Create user-organization relationship
      await UserOrganization.create({
        user_id: user.id,
        organization_id: organization.id,
        is_primary: true,
        status: 'active'
      });

      // Create user-role relationship
      await UserRole.create({
        user_id: user.id,
        role_id: userData.role.id,
        organization_id: organization.id,
        is_primary: true,
        status: 'active'
      });

      console.log(`Created user: ${userData.email} with role: ${userData.role.name}`);
    }

    console.log('✅ User seeding completed successfully');
    console.log('\nTest credentials:');
    console.log('Email: <EMAIL>, Password: password');
    console.log('Email: <EMAIL>, Password: password');
    console.log('Email: <EMAIL>, Password: password');
    console.log('Email: <EMAIL>, Password: password');
    console.log('Email: <EMAIL>, Password: vedved123');

  } catch (error) {
    console.error('❌ Error seeding users:', error);
  } finally {
    await sequelize.close();
  }
}

// Run the seeding function
seedUsers();
