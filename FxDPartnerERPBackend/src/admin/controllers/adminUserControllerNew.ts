import { Request, Response } from 'express';
import { User, UserOrganization, UserRole, Organization, Role } from '../../models';
import { Page } from '../../models/Page';
import { UserPagePermission } from '../../models/UserPagePermission';
import { AdminRequest } from '../middleware/adminAuth';
import { UserManagementService, CreateUserWithMultipleOrgsAndRoles } from '../services/userManagementService';

export class AdminUserController {
  // Get all users with their organizations and roles
  static async getAllUsers(req: AdminRequest, res: Response) {
    try {
      const { page = 1, limit = 10, search = '', status = '', organizationId = '' } = req.query;
      const offset = (Number(page) - 1) * Number(limit);

      // Build where clause for users
      const whereClause: any = {};
      
      if (search) {
        whereClause[require('sequelize').Op.or] = [
          { first_name: { [require('sequelize').Op.iLike]: `%${search}%` } },
          { email: { [require('sequelize').Op.iLike]: `%${search}%` } }
        ];
      }
      
      if (status && status !== 'all') {
        whereClause.status = status;
      }

      // Build include clause
      const includeClause: any[] = [
        {
          model: UserOrganization,
          include: [Organization],
          ...(organizationId && organizationId !== 'all' ? {
            where: { organization_id: organizationId, status: 'active' }
          } : {})
        },
        {
          model: UserRole,
          include: [Role, Organization]
        }
      ];

      const { count, rows: users } = await User.findAndCountAll({
        where: whereClause,
        limit: Number(limit),
        offset,
        order: [['created_at', 'DESC']],
        include: includeClause,
        attributes: { exclude: ['password_hash'] },
        distinct: true
      });

      res.json({
        success: true,
        message: 'Users retrieved successfully',
        data: {
          users,
          pagination: {
            total: count,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(count / Number(limit))
          }
        }
      });
    } catch (error) {
      console.error('Get users error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching users'
      });
    }
  }

  // Get user by ID with organizations and roles
  static async getUserById(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;

      const user = await UserManagementService.getUserWithAssociations(id);

      res.json({
        success: true,
        message: 'User retrieved successfully',
        data: { user }
      });
    } catch (error: any) {
      console.error('Get user by ID error:', error);
      if (error.message === 'User not found') {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching user'
      });
    }
  }

  // Create simple user without organizations and roles
  static async createSimpleUser(req: AdminRequest, res: Response) {
    try {
      const { email, password, first_name, phone, status, organization_id } = req.body;

      // Validate required fields
      if (!email || !password || !first_name) {
        return res.status(400).json({
          success: false,
          message: 'Email, password, and first name are required'
        });
      }

      // Check if user with email already exists
      const existingUser = await User.findOne({ where: { email } });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User with this email already exists'
        });
      }

      // Validate organization_id if provided
      if (organization_id) {
        const organization = await Organization.findByPk(organization_id);
        if (!organization) {
          return res.status(400).json({
            success: false,
            message: 'Invalid organization ID'
          });
        }
      }

      // Hash password
      const bcrypt = require('bcrypt');
      const saltRounds = 10;
      const password_hash = await bcrypt.hash(password, saltRounds);

      // Create user
      const user = await User.create({
        email,
        password_hash,
        first_name,
        phone: phone || null,
        organization_id: organization_id || null,
        status: status || 'pending'
      });

      // Give default dashboard permission
      const dashboardPage = await Page.findOne({ where: { name: 'dashboard' } });
      if (dashboardPage) {
        await UserPagePermission.create({
          user_id: user.id,
          page_id: dashboardPage.id,
          can_view: true,
          can_edit: false
        });
      }

      // Return user without password
      const { password_hash: _, ...userWithoutPassword } = user.toJSON();

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: { user: userWithoutPassword }
      });
    } catch (error: any) {
      console.error('Create simple user error:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error while creating user'
      });
    }
  }

  // Create user with multiple organizations and roles
  static async createUser(req: AdminRequest, res: Response) {
    try {
      const userData: CreateUserWithMultipleOrgsAndRoles = req.body;

      // Validate required fields
      if (!userData.email || !userData.password || !userData.first_name) {
        return res.status(400).json({
          success: false,
          message: 'Email, password, and first name are required'
        });
      }

      if (!userData.organizations || userData.organizations.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'At least one organization must be specified'
        });
      }

      // Check if user with email already exists
      const existingUser = await User.findOne({ where: { email: userData.email } });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User with this email already exists'
        });
      }

      // Validate organizations exist
      for (const orgData of userData.organizations) {
        const organization = await Organization.findByPk(orgData.organization_id);
        if (!organization) {
          return res.status(400).json({
            success: false,
            message: `Invalid organization ID: ${orgData.organization_id}`
          });
        }

        // Validate roles exist
        for (const roleData of orgData.roles) {
          const role = await Role.findByPk(roleData.role_id);
          if (!role) {
            return res.status(400).json({
              success: false,
              message: `Invalid role ID: ${roleData.role_id}`
            });
          }
        }
      }

      const user = await UserManagementService.createUserWithOrganizationsAndRoles(
        userData,
        req.admin?.username
      );

      // Give default dashboard permission
      const dashboardPage = await Page.findOne({ where: { name: 'dashboard' } });
      if (dashboardPage) {
        await UserPagePermission.create({
          user_id: user.id,
          page_id: dashboardPage.id,
          can_view: true,
          can_edit: false
        });
      }

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: { user }
      });
    } catch (error: any) {
      console.error('Create user error:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error while creating user'
      });
    }
  }

  // Update user basic information
  static async updateUser(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;
      const { first_name, email, phone, status } = req.body;

      const user = await User.findByPk(id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Validate required fields
      if (!first_name || !email) {
        return res.status(400).json({
          success: false,
          message: 'First name and email are required'
        });
      }

      // Check if another user with same email exists
      if (email !== user.email) {
        const existingUser = await User.findOne({
          where: { 
            email,
            id: { [require('sequelize').Op.ne]: id }
          }
        });

        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: 'Another user with this email already exists'
          });
        }
      }

      // Update user
      await user.update({
        first_name,
        email,
        phone: phone || user.phone,
        status: status || user.status
      });

      // Fetch updated user with associations
      const updatedUser = await UserManagementService.getUserWithAssociations(id);

      res.json({
        success: true,
        message: 'User updated successfully',
        data: { user: updatedUser }
      });
    } catch (error: any) {
      console.error('Update user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while updating user'
      });
    }
  }

  // Add user to organization with roles
  static async addUserToOrganization(req: AdminRequest, res: Response) {
    try {
      const { userId } = req.params;
      const { organization_id, role_ids, is_primary_org, primary_role_id } = req.body;

      if (!organization_id || !role_ids || !Array.isArray(role_ids) || role_ids.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Organization ID and role IDs are required'
        });
      }

      // Validate organization exists
      const organization = await Organization.findByPk(organization_id);
      if (!organization) {
        return res.status(400).json({
          success: false,
          message: 'Invalid organization ID'
        });
      }

      // Validate roles exist
      for (const roleId of role_ids) {
        const role = await Role.findByPk(roleId);
        if (!role) {
          return res.status(400).json({
            success: false,
            message: `Invalid role ID: ${roleId}`
          });
        }
      }

      await UserManagementService.addUserToOrganization(
        userId,
        organization_id,
        role_ids,
        {
          is_primary_org,
          primary_role_id,
          assigned_by: req.admin?.username
        }
      );

      res.json({
        success: true,
        message: 'User added to organization successfully'
      });
    } catch (error: any) {
      console.error('Add user to organization error:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error while adding user to organization'
      });
    }
  }

  // Remove user from organization
  static async removeUserFromOrganization(req: AdminRequest, res: Response) {
    try {
      const { userId, organizationId } = req.params;

      await UserManagementService.removeUserFromOrganization(userId, organizationId);

      res.json({
        success: true,
        message: 'User removed from organization successfully'
      });
    } catch (error: any) {
      console.error('Remove user from organization error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while removing user from organization'
      });
    }
  }

  // Update user roles in organization
  static async updateUserRolesInOrganization(req: AdminRequest, res: Response) {
    try {
      const { userId, organizationId } = req.params;
      const { roles } = req.body;

      if (!Array.isArray(roles) || roles.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Roles array is required'
        });
      }

      // Validate roles exist
      for (const roleData of roles) {
        const role = await Role.findByPk(roleData.role_id);
        if (!role) {
          return res.status(400).json({
            success: false,
            message: `Invalid role ID: ${roleData.role_id}`
          });
        }
      }

      await UserManagementService.updateUserRolesInOrganization(
        userId,
        organizationId,
        roles,
        req.admin?.username
      );

      res.json({
        success: true,
        message: 'User roles updated successfully'
      });
    } catch (error: any) {
      console.error('Update user roles error:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error while updating user roles'
      });
    }
  }

  // Get users by organization
  static async getUsersByOrganization(req: AdminRequest, res: Response) {
    try {
      const { organizationId } = req.params;

      const organization = await Organization.findByPk(organizationId);
      if (!organization) {
        return res.status(404).json({
          success: false,
          message: 'Organization not found'
        });
      }

      const users = await UserManagementService.getUsersByOrganization(organizationId);

      res.json({
        success: true,
        message: 'Users retrieved successfully',
        data: {
          organization: {
            id: organization.id,
            name: organization.name
          },
          users
        }
      });
    } catch (error) {
      console.error('Get users by organization error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching users'
      });
    }
  }

  // Get user statistics
  static async getUserStats(req: AdminRequest, res: Response) {
    try {
      const totalUsers = await User.count();
      const activeUsers = await User.count({ where: { status: 'active' } });
      const inactiveUsers = await User.count({ where: { status: 'inactive' } });
      const pendingUsers = await User.count({ where: { status: 'pending' } });

      // Get role distribution from user_roles table
      const roleStats = await UserRole.findAll({
        include: [Role],
        where: { status: 'active' },
        attributes: ['role_id'],
        group: ['role_id', 'role.id', 'role.name'],
        raw: false
      });

      const roleDistribution: { [key: string]: number } = {};
      roleStats.forEach((userRole: any) => {
        const roleName = userRole.role?.name || 'unknown';
        roleDistribution[roleName] = (roleDistribution[roleName] || 0) + 1;
      });

      res.json({
        success: true,
        message: 'User statistics retrieved successfully',
        data: {
          totalUsers,
          activeUsers,
          inactiveUsers,
          pendingUsers,
          roleDistribution
        }
      });
    } catch (error) {
      console.error('Get user stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching statistics'
      });
    }
  }

  // Get user page permissions
  static async getUserPagePermissions(req: AdminRequest, res: Response) {
    try {
      const { userId } = req.params;

      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Get all pages
      const pages = await Page.findAll({
        where: { is_active: true },
        order: [['category', 'ASC'], ['sort_order', 'ASC']]
      });

      // Get user's current permissions
      const userPermissions = await UserPagePermission.findAll({
        where: { user_id: userId },
        include: [
          {
            model: Page,
            as: 'page'
          }
        ]
      });

      // Create a map of page permissions
      const permissionMap = new Map();
      userPermissions.forEach(permission => {
        permissionMap.set(permission.page_id, {
          can_view: permission.can_view,
          can_edit: permission.can_edit
        });
      });

      // Build response with all pages and their permissions
      const pagePermissions = pages.map(page => ({
        page_id: page.id,
        page_name: page.name,
        display_name: page.display_name,
        category: page.category,
        can_view: permissionMap.get(page.id)?.can_view || false,
        can_edit: permissionMap.get(page.id)?.can_edit || false
      }));

      res.json({
        success: true,
        message: 'User page permissions retrieved successfully',
        data: {
          user: {
            id: user.id,
            first_name: user.first_name,
            email: user.email
          },
          page_permissions: pagePermissions
        }
      });
    } catch (error) {
      console.error('Get user page permissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while fetching user page permissions'
      });
    }
  }

  // Update user page permissions
  static async updateUserPagePermissions(req: AdminRequest, res: Response) {
    try {
      const { userId } = req.params;
      const { permissions } = req.body;

      const user = await User.findByPk(userId);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      if (!Array.isArray(permissions)) {
        return res.status(400).json({
          success: false,
          message: 'Permissions must be an array'
        });
      }

      // Validate permissions format
      for (const permission of permissions) {
        if (!permission.page_id || typeof permission.can_view !== 'boolean' || typeof permission.can_edit !== 'boolean') {
          return res.status(400).json({
            success: false,
            message: 'Invalid permission format. Each permission must have page_id, can_view, and can_edit'
          });
        }

        // Auto-select view if edit is selected
        if (permission.can_edit && !permission.can_view) {
          permission.can_view = true;
        }
      }

      // Delete existing permissions
      await UserPagePermission.destroy({
        where: { user_id: userId }
      });

      // Create new permissions
      const newPermissions = permissions
        .filter(p => p.can_view || p.can_edit) // Only create records for granted permissions
        .map(permission => ({
          user_id: userId,
          page_id: permission.page_id,
          can_view: permission.can_view,
          can_edit: permission.can_edit
        }));

      if (newPermissions.length > 0) {
        await UserPagePermission.bulkCreate(newPermissions);
      }

      res.json({
        success: true,
        message: 'User page permissions updated successfully',
        data: {
          updated_permissions: newPermissions.length
        }
      });
    } catch (error) {
      console.error('Update user page permissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while updating user page permissions'
      });
    }
  }

  // Update user assignments (organizations and roles) atomically
  static async updateUserAssignments(req: AdminRequest, res: Response) {
    try {
      const { userId } = req.params;
      const { assignments } = req.body;

      if (!assignments || !assignments.organizations || !Array.isArray(assignments.organizations)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid assignments data. Expected organizations array.'
        });
      }

      // Validate that at least one organization is provided
      if (assignments.organizations.length === 0) {
        return res.status(400).json({
          success: false,
          message: 'At least one organization must be assigned to the user'
        });
      }

      // Validate assignment structure
      for (const org of assignments.organizations) {
        if (!org.organization_id || !Array.isArray(org.roles)) {
          return res.status(400).json({
            success: false,
            message: 'Each organization must have organization_id and roles array'
          });
        }

        // Roles are now optional for organizations since they are independent

        for (const role of org.roles) {
          if (!role.role_id) {
            return res.status(400).json({
              success: false,
              message: 'Each role must have a role_id'
            });
          }
        }
      }

      await UserManagementService.updateUserAssignments(
        userId,
        assignments,
        req.admin?.username
      );

      // Fetch updated user with associations
      const updatedUser = await UserManagementService.getUserWithAssociations(userId);

      res.json({
        success: true,
        message: 'User assignments updated successfully',
        data: { user: updatedUser }
      });
    } catch (error: any) {
      console.error('Update user assignments error:', error);
      
      // Return more specific error messages for validation errors
      if (error.message && (
        error.message.includes('User not found') ||
        error.message.includes('Invalid organization ID') ||
        error.message.includes('Invalid role ID') ||
        error.message.includes('primary')
      )) {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      
      res.status(500).json({
        success: false,
        message: error.message || 'Internal server error while updating user assignments'
      });
    }
  }

  // Delete user
  static async deleteUser(req: AdminRequest, res: Response) {
    try {
      const { id } = req.params;

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Delete user (related records will be deleted automatically due to CASCADE)
      await user.destroy();

      res.json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      console.error('Delete user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while deleting user'
      });
    }
  }
}
