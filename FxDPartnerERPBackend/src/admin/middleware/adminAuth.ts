import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';

// Admin credentials - hardcoded as requested
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123'
};

// Admin JWT secret (in production, this should be in environment variables)
const ADMIN_JWT_SECRET = 'admin-jwt-secret-key-2024';

export interface AdminRequest extends Request {
  admin?: {
    username: string;
    loginTime: Date;
  };
}

// Admin authentication middleware
export const adminAuth = (req: AdminRequest, res: Response, next: NextFunction) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No admin token provided.'
      });
    }

    const decoded = jwt.verify(token, ADMIN_JWT_SECRET) as any;
    
    if (decoded.type !== 'admin') {
      return res.status(401).json({
        success: false,
        message: 'Access denied. Invalid admin token.'
      });
    }

    req.admin = {
      username: decoded.username,
      loginTime: new Date(decoded.loginTime)
    };

    next();
  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Invalid admin token.'
    });
  }
};

// Admin login validation
export const validateAdminCredentials = (username: string, password: string): boolean => {
  return username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password;
};

// Generate admin JWT token
export const generateAdminToken = (username: string): string => {
  return jwt.sign(
    {
      username,
      type: 'admin',
      loginTime: new Date().toISOString()
    },
    ADMIN_JWT_SECRET,
    { expiresIn: '24h' }
  );
};
