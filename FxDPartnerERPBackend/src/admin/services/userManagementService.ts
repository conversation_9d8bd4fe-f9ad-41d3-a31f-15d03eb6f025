import { Transaction } from 'sequelize';
import { User, CreateUserData, UserOrganization, UserRole, Organization, Role } from '../../models';
import bcrypt from 'bcrypt';
import sequelize from '../../config/sequelize';

export interface CreateUserWithMultipleOrgsAndRoles {
  email: string;
  password: string;
  first_name: string;
  phone?: string;
  status?: 'active' | 'inactive' | 'pending';
  organizations: {
    organization_id: string;
    is_primary?: boolean;
    status?: 'active' | 'inactive' | 'pending';
    roles: {
      role_id: string;
      is_primary?: boolean;
      status?: 'active' | 'inactive';
      assigned_by?: string;
    }[];
  }[];
}

export class UserManagementService {
  /**
   * Create a user with multiple organizations and roles
   */
  static async createUserWithOrganizationsAndRoles(
    userData: CreateUserWithMultipleOrgsAndRoles,
    createdBy?: string
  ): Promise<User> {
    const transaction = await sequelize.transaction();

    try {
      // Hash password
      const saltRounds = 10;
      const password_hash = await bcrypt.hash(userData.password, saltRounds);

      // Create the user
      const user = await User.create({
        email: userData.email,
        password_hash,
        first_name: userData.first_name,
        phone: userData.phone,
        status: userData.status || 'pending'
      }, { transaction });

      // Validate that at least one organization is marked as primary
      const primaryOrgs = userData.organizations.filter(org => org.is_primary);
      if (primaryOrgs.length === 0) {
        // If no primary org specified, make the first one primary
        userData.organizations[0].is_primary = true;
      } else if (primaryOrgs.length > 1) {
        throw new Error('Only one organization can be marked as primary');
      }

      // Create user-organization relationships
      for (const orgData of userData.organizations) {
        const userOrg = await UserOrganization.create({
          user_id: user.id,
          organization_id: orgData.organization_id,
          is_primary: orgData.is_primary || false,
          status: orgData.status || 'active'
        }, { transaction });

        // Validate that at least one role per organization is marked as primary
        const primaryRoles = orgData.roles.filter(role => role.is_primary);
        if (primaryRoles.length === 0 && orgData.roles.length > 0) {
          // If no primary role specified, make the first one primary
          orgData.roles[0].is_primary = true;
        } else if (primaryRoles.length > 1) {
          throw new Error(`Only one role can be marked as primary for organization ${orgData.organization_id}`);
        }

        // Create user-role relationships for this organization
        for (const roleData of orgData.roles) {
          await UserRole.create({
            user_id: user.id,
            role_id: roleData.role_id,
            organization_id: orgData.organization_id,
            is_primary: roleData.is_primary || false,
            status: roleData.status || 'active',
            assigned_by: roleData.assigned_by || createdBy
          }, { transaction });
        }
      }

      await transaction.commit();

      // Return user with associations
      return await UserManagementService.getUserWithAssociations(user.id);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Add user to an organization with roles
   */
  static async addUserToOrganization(
    userId: string,
    organizationId: string,
    roleIds: string[],
    options: {
      is_primary_org?: boolean;
      org_status?: 'active' | 'inactive' | 'pending';
      primary_role_id?: string;
      assigned_by?: string;
    } = {}
  ): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      // Check if user is already in this organization
      const existingUserOrg = await UserOrganization.findOne({
        where: { user_id: userId, organization_id: organizationId }
      });

      if (existingUserOrg) {
        throw new Error('User is already a member of this organization');
      }

      // If this is marked as primary, update other organizations to non-primary
      if (options.is_primary_org) {
        await UserOrganization.update(
          { is_primary: false },
          { 
            where: { user_id: userId },
            transaction 
          }
        );
      }

      // Create user-organization relationship
      await UserOrganization.create({
        user_id: userId,
        organization_id: organizationId,
        is_primary: options.is_primary_org || false,
        status: options.org_status || 'active'
      }, { transaction });

      // Add roles for this organization
      const primaryRoleId = options.primary_role_id || roleIds[0];
      
      for (const roleId of roleIds) {
        await UserRole.create({
          user_id: userId,
          role_id: roleId,
          organization_id: organizationId,
          is_primary: roleId === primaryRoleId,
          status: 'active',
          assigned_by: options.assigned_by
        }, { transaction });
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Remove user from an organization
   */
  static async removeUserFromOrganization(
    userId: string,
    organizationId: string
  ): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      // Remove all roles for this user in this organization
      await UserRole.destroy({
        where: { 
          user_id: userId,
          organization_id: organizationId 
        },
        transaction
      });

      // Remove user-organization relationship
      await UserOrganization.destroy({
        where: { 
          user_id: userId,
          organization_id: organizationId 
        },
        transaction
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update user roles in an organization
   */
  static async updateUserRolesInOrganization(
    userId: string,
    organizationId: string,
    roleUpdates: {
      role_id: string;
      is_primary?: boolean;
      status?: 'active' | 'inactive';
    }[],
    assignedBy?: string
  ): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      // Remove existing roles for this user in this organization
      await UserRole.destroy({
        where: { 
          user_id: userId,
          organization_id: organizationId 
        },
        transaction
      });

      // Ensure only one role is marked as primary
      const primaryRoles = roleUpdates.filter(role => role.is_primary);
      if (primaryRoles.length > 1) {
        throw new Error('Only one role can be marked as primary');
      } else if (primaryRoles.length === 0 && roleUpdates.length > 0) {
        roleUpdates[0].is_primary = true;
      }

      // Add updated roles
      for (const roleUpdate of roleUpdates) {
        await UserRole.create({
          user_id: userId,
          role_id: roleUpdate.role_id,
          organization_id: organizationId,
          is_primary: roleUpdate.is_primary || false,
          status: roleUpdate.status || 'active',
          assigned_by: assignedBy
        }, { transaction });
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get user with all organizations and roles
   */
  static async getUserWithAssociations(userId: string): Promise<User> {
    const user = await User.findByPk(userId, {
      include: [
        {
          model: UserOrganization,
          include: [Organization]
        },
        {
          model: UserRole,
          include: [Role, Organization]
        }
      ]
    });

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  }

  /**
   * Get all users with their organizations and roles
   */
  static async getAllUsersWithAssociations(): Promise<User[]> {
    return await User.findAll({
      include: [
        {
          model: UserOrganization,
          include: [Organization]
        },
        {
          model: UserRole,
          include: [Role, Organization]
        }
      ],
      order: [['created_at', 'DESC']]
    });
  }

  /**
   * Get users by organization
   */
  static async getUsersByOrganization(organizationId: string): Promise<User[]> {
    return await User.findAll({
      include: [
        {
          model: UserOrganization,
          where: { organization_id: organizationId, status: 'active' },
          include: [Organization]
        },
        {
          model: UserRole,
          where: { organization_id: organizationId, status: 'active' },
          include: [Role]
        }
      ]
    });
  }

  /**
   * Update user assignments (organizations and roles) atomically
   * Roles are now independent of organizations
   */
  static async updateUserAssignments(
    userId: string,
    assignments: {
      organizations: {
        organization_id: string;
        is_primary: boolean;
        roles: {
          role_id: string;
          is_primary: boolean;
        }[];
      }[];
      roles?: {
        role_id: string;
        is_primary: boolean;
      }[];
    },
    assignedBy?: string
  ): Promise<void> {
    const transaction = await sequelize.transaction();

    try {
      // Validate that user exists
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Validate that only one organization is marked as primary
      const primaryOrgs = assignments.organizations.filter(org => org.is_primary);
      if (primaryOrgs.length > 1) {
        throw new Error('Only one organization can be marked as primary');
      }
      
      // If no organization is marked as primary, make the first one primary
      if (primaryOrgs.length === 0 && assignments.organizations.length > 0) {
        assignments.organizations[0].is_primary = true;
      }

      // Validate organizations exist
      for (const orgData of assignments.organizations) {
        const organization = await Organization.findByPk(orgData.organization_id);
        if (!organization) {
          throw new Error(`Invalid organization ID: ${orgData.organization_id}`);
        }
      }

      // Collect all roles from both organization-based and independent role assignments
      const allRolesMap = new Map<string, { role_id: string; is_primary: boolean }>();
      
      // Add roles from organizations (for backward compatibility)
      for (const orgData of assignments.organizations) {
        for (const role of orgData.roles) {
          // If role already exists, keep the primary flag if any instance is primary
          const existing = allRolesMap.get(role.role_id);
          allRolesMap.set(role.role_id, {
            role_id: role.role_id,
            is_primary: (existing?.is_primary || role.is_primary) || false
          });
        }
      }
      
      // Add independent roles if provided
      if (assignments.roles) {
        for (const role of assignments.roles) {
          const existing = allRolesMap.get(role.role_id);
          allRolesMap.set(role.role_id, {
            role_id: role.role_id,
            is_primary: (existing?.is_primary || role.is_primary) || false
          });
        }
      }
      
      // Convert map back to array
      const allRoles = Array.from(allRolesMap.values());

      // Validate roles exist
      for (const roleData of allRoles) {
        const role = await Role.findByPk(roleData.role_id);
        if (!role) {
          throw new Error(`Invalid role ID: ${roleData.role_id}`);
        }
      }

      // Validate that only one role is marked as primary
      const primaryRoles = allRoles.filter(role => role.is_primary);
      if (primaryRoles.length > 1) {
        throw new Error('Only one role can be marked as primary');
      }

      // Get current assignments
      const currentUserOrgs = await UserOrganization.findAll({
        where: { user_id: userId },
        transaction
      });

      const currentUserRoles = await UserRole.findAll({
        where: { user_id: userId },
        transaction
      });

      // Handle organization assignments
      const newOrgIds = new Set(assignments.organizations.map(org => org.organization_id));
      
      // Remove organizations that are no longer assigned
      const orgsToRemove = currentUserOrgs.filter(uo => !newOrgIds.has(uo.organization_id));
      for (const userOrg of orgsToRemove) {
        await userOrg.destroy({ transaction });
      }

      // Process each organization assignment
      for (const orgData of assignments.organizations) {
        const existingUserOrg = currentUserOrgs.find(uo => uo.organization_id === orgData.organization_id);

        if (existingUserOrg) {
          // Update existing organization assignment
          await existingUserOrg.update({
            is_primary: orgData.is_primary,
            status: 'active'
          }, { transaction });
        } else {
          // Create new organization assignment
          await UserOrganization.create({
            user_id: userId,
            organization_id: orgData.organization_id,
            is_primary: orgData.is_primary,
            status: 'active'
          }, { transaction });
        }
      }

      // Handle role assignments (independent of organizations)
      // Remove all existing role assignments
      await UserRole.destroy({
        where: { user_id: userId },
        transaction
      });

      // Create new role assignments
      // Since roles are independent, we'll assign them to the primary organization
      const primaryOrgId = assignments.organizations.find(org => org.is_primary)?.organization_id || 
                          assignments.organizations[0]?.organization_id;

      for (const roleData of allRoles) {
        await UserRole.create({
          user_id: userId,
          role_id: roleData.role_id,
          organization_id: primaryOrgId, // Assign to primary organization for compatibility
          is_primary: roleData.is_primary,
          status: 'active',
          assigned_by: assignedBy
        }, { transaction });
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
}
