import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import multer from 'multer';
import { testConnection } from './config/database';
import sequelize, { testSequelizeConnection } from './config/sequelize';
import partnersRoutes from './routes/partners';
import procurementRoutes from './routes/procurementRoutes';
import salesRoutes from './routes/salesRoutes';
import productRoutes from './routes/productRoutes';
import inventoryRoutes from './routes/inventoryRoutes';
import paymentRoutes from './routes/paymentRoutes';
import customerRoutes from './routes/customerRoutes';
import supplierRoutes from './routes/supplierRoutes';
import purchaseRecordRoutes from './routes/purchaseRecordRoutes';
import vehicleArrivalRoutes from './routes/vehicleArrivalRoutes';
import authRoutes from './routes/authRoutes';
import roleRoutes from './routes/roleRoutes';

// Admin routes
import adminAuthRoutes from './admin/routes/adminAuthRoutes';
import adminOrganizationRoutes from './admin/routes/adminOrganizationRoutes';
import adminUserRoutes from './admin/routes/adminUserRoutes';
import adminRoleRoutes from './admin/routes/adminRoleRoutes';

// Load environment variables
dotenv.config();

const app: Application = express();
const PORT = process.env.PORT || 9000

// Middleware
app.use(helmet()); // Security headers
app.use(cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:5174',
    'https://fxdpartner-staging.vegrow.in',
    ...(process.env.CORS_ORIGIN ? [process.env.CORS_ORIGIN] : [])
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-organization-id']
}));
app.use(morgan('combined')); // Logging

// URL-encoded middleware (safe for all routes)
app.use(express.urlencoded({ extended: true }));

// JSON middleware - applied selectively to avoid conflicts with multipart routes
const jsonMiddleware = express.json({ limit: '10mb' });

// Health check endpoint
app.get('/health', (req: Request, res: Response) => {
  res.status(200).json({
    status: 'OK',
    message: 'FXD Partner ERP Backend is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API Routes - Apply JSON middleware to routes that need it, exclude multipart routes
app.use('/api/auth', jsonMiddleware, authRoutes);
// Also add direct auth route for backward compatibility
app.use('/auth', jsonMiddleware, authRoutes);
app.use('/api/partners', jsonMiddleware, partnersRoutes);
app.use('/api/procurement', jsonMiddleware, procurementRoutes);
app.use('/api/sales', jsonMiddleware, salesRoutes);
app.use('/api/products', jsonMiddleware, productRoutes);
app.use('/api/inventory', jsonMiddleware, inventoryRoutes);
app.use('/api/payments', paymentRoutes); // No JSON middleware - uses multer for FormData
app.use('/api/customers', jsonMiddleware, customerRoutes);
app.use('/api/suppliers', jsonMiddleware, supplierRoutes);
app.use('/api/purchase-records', jsonMiddleware, purchaseRecordRoutes);
app.use('/api/vehicle-arrivals', vehicleArrivalRoutes); // No JSON middleware - may use FormData for attachments
app.use('/api/roles', jsonMiddleware, roleRoutes);

// Admin API Routes
app.use('/api/admin/auth', jsonMiddleware, adminAuthRoutes);
app.use('/api/admin/organizations', jsonMiddleware, adminOrganizationRoutes);
app.use('/api/admin/users', jsonMiddleware, adminUserRoutes);
app.use('/api/admin/roles', jsonMiddleware, adminRoleRoutes);

app.get('/api', (req: Request, res: Response) => {
  res.json({
    message: 'Welcome to FXD Partner ERP API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      api: '/api',
      auth: '/api/auth',
      partners: '/api/partners',
      procurement: '/api/procurement',
      sales: '/api/sales',
      products: '/api/products',
      inventory: '/api/inventory',
      payments: '/api/payments',
      customers: '/api/customers',
      suppliers: '/api/suppliers',
      purchaseRecords: '/api/purchase-records',
      vehicleArrivals: '/api/vehicle-arrivals',
      roles: '/api/roles',
      admin: {
        auth: '/api/admin/auth',
        organizations: '/api/admin/organizations',
        users: '/api/admin/users',
        roles: '/api/admin/roles'
      }
    }
  });
});

// 404 handler
app.use('*', (req: Request, res: Response) => {
  res.status(404).json({
    error: 'Route not found',
    message: `The requested route ${req.originalUrl} does not exist`
  });
});

// Global error handler
app.use((err: any, req: Request, res: Response, next: any) => {
  console.error('Global error handler:', err);
  res.status(err.status || 500).json({
    error: 'Internal Server Error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Start server
const startServer = async () => {
  try {
    // Test database connections
    const dbConnected = await testConnection();
    const sequelizeConnected = await testSequelizeConnection();
    
    if (!dbConnected && !sequelizeConnected) {
      console.warn('⚠️  Database connection failed, but server will start anyway');
    }

    // Initialize Sequelize models
    if (sequelizeConnected) {
      try {
        await sequelize.sync({ alter: false }); // Don't alter tables in production
        console.log('✅ Sequelize models synchronized');
      } catch (error) {
        console.error('❌ Failed to sync Sequelize models:', error);
      }
    }

    app.listen(PORT, () => {
      console.log(`🚀 Server is running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🌐 CORS Origin: ${process.env.CORS_ORIGIN || 'http://localhost:3000'}`);
      console.log(`💾 Database: ${process.env.DB_HOST || 'localhost'}:${process.env.DB_PORT || '3306'}`);
      console.log(`🔗 ORM: Sequelize with TypeScript models`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

export default app;
