import { Sequelize } from 'sequelize-typescript';
import dotenv from 'dotenv';
import { Organization } from '../models/Organization';
import { User } from '../models/User';
import { UserDetails } from '../models/UserDetails';
import { UserOrganization } from '../models/UserOrganization';
import { UserRole } from '../models/UserRole';
import { Role } from '../models/Role';
import { Page } from '../models/Page';
import { UserPagePermission } from '../models/UserPagePermission';
import { Customer } from '../models/Customer';
import { Supplier } from '../models/Supplier';
import { Product, SKU } from '../models/Product';
import { CurrentInventory } from '../models/Inventory';
import { SalesOrder, SalesOrderItem, SalesOrderPayment } from '../models/SalesOrder';
import { VehicleArrival, VehicleArrivalItem, VehicleArrivalAttachment } from '../models/VehicleArrival';
import { PurchaseRecord, PurchaseRecordItem, PurchaseRecordCost } from '../models/PurchaseRecord';
import { Payment, CustomerCreditExtension } from '../models/Payment';
import { GRNReturnPDDRequest } from '../models/GRNReturnPDDRequest';
import { GRNReturnPDDItem } from '../models/GRNReturnPDDItem';
import { InventoryTransaction } from '../models/InventoryTransaction';

dotenv.config();

const sequelize = new Sequelize({
  database: process.env.DB_NAME || 'fxd_partner_erp_backend',
  dialect: 'mysql',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  models: [
    Organization,
    User,
    UserDetails,
    UserOrganization,
    UserRole,
    Role,
    Page,
    UserPagePermission,
    Customer,
    Supplier,
    Product,
    SKU,
    CurrentInventory,
    SalesOrder,
    SalesOrderItem,
    SalesOrderPayment,
    VehicleArrival,
    VehicleArrivalItem,
    VehicleArrivalAttachment,
    PurchaseRecord,
    PurchaseRecordItem,
    PurchaseRecordCost,
    Payment,
    CustomerCreditExtension,
    GRNReturnPDDRequest,
    GRNReturnPDDItem,
    InventoryTransaction
  ],
  logging: process.env.NODE_ENV === 'development' ? console.log : false,
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true,
  },
});

export const testSequelizeConnection = async (): Promise<boolean> => {
  try {
    await sequelize.authenticate();
    console.log('✅ Sequelize database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Sequelize database connection failed:', error);
    return false;
  }
};

export default sequelize;
