import { Transaction } from 'sequelize';
import { 
  InventoryTransaction, 
  CreateInventoryTransactionData, 
  InventoryTransactionFilters,
  CurrentInventory,
  Product,
  SKU,
  User
} from '../models';
import { Op } from 'sequelize';

export class InventoryTransactionService {
  /**
   * Create an inventory transaction and update the current inventory
   */
  static async createInventoryTransaction(
    data: CreateInventoryTransactionData,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    console.log('Creating inventory transaction with data:', data);
    
    // Create the inventory transaction record
    const inventoryTransaction = await InventoryTransaction.create(data as any, { transaction });
    console.log('Inventory transaction created:', inventoryTransaction.id);

    // Check if inventory record exists
    const existingInventory = await CurrentInventory.findOne({
      where: {
        product_id: data.product_id,
        sku_id: data.sku_id,
        organization_id: data.organization_id
      },
      transaction
    });
    
    console.log('Existing inventory record:', existingInventory ? existingInventory.id : 'NOT FOUND');
    console.log('Current available_quantity:', existingInventory ? existingInventory.available_quantity : 'N/A');
    console.log('Quantity change:', data.quantity_change);

    if (!existingInventory) {
      console.error('No inventory record found for product_id:', data.product_id, 'sku_id:', data.sku_id);
      throw new Error(`No inventory record found for product ${data.product_id} and SKU ${data.sku_id}`);
    }

    // Update the current inventory based on the quantity change
    const [affectedRows] = await CurrentInventory.increment('available_quantity', {
      by: data.quantity_change,
      where: {
        product_id: data.product_id,
        sku_id: data.sku_id,
        organization_id: data.organization_id
      },
      transaction
    });
    
    console.log('Inventory increment affected rows:', affectedRows);

    // Update weight if provided
    if (data.weight_change && data.weight_change !== 0) {
      await CurrentInventory.increment('total_weight', {
        by: data.weight_change,
        where: {
          product_id: data.product_id,
          sku_id: data.sku_id,
          organization_id: data.organization_id
        },
        transaction
      });
    }

    // Update last_updated_at timestamp
    await CurrentInventory.update(
      { last_updated_at: new Date() },
      {
        where: {
          product_id: data.product_id,
          sku_id: data.sku_id,
          organization_id: data.organization_id
        },
        transaction
      }
    );

    return inventoryTransaction;
  }

  /**
   * Get inventory transaction history for a specific product/SKU
   */
  static async getInventoryTransactionHistory(
    organizationId: string,
    productId: string,
    skuId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ transactions: InventoryTransaction[]; total: number }> {
    const { rows: transactions, count: total } = await InventoryTransaction.findAndCountAll({
      where: {
        organization_id: organizationId,
        product_id: productId,
        sku_id: skuId
      },
      include: [
        {
          model: Product,
          attributes: ['name', 'category']
        },
        {
          model: SKU,
          attributes: ['sku_code', 'unit_type']
        },
        {
          model: User,
          as: 'performer',
          attributes: ['name', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    return { transactions, total };
  }

  /**
   * Get transactions by reference (e.g., all transactions for a GRN request)
   */
  static async getTransactionsByReference(
    organizationId: string,
    referenceType: string,
    referenceId: string
  ): Promise<InventoryTransaction[]> {
    return await InventoryTransaction.findAll({
      where: {
        organization_id: organizationId,
        reference_type: referenceType,
        reference_id: referenceId
      },
      include: [
        {
          model: Product,
          attributes: ['name', 'category']
        },
        {
          model: SKU,
          attributes: ['sku_code', 'unit_type']
        },
        {
          model: User,
          as: 'performer',
          attributes: ['name', 'email']
        }
      ],
      order: [['created_at', 'DESC']]
    });
  }

  /**
   * Get filtered inventory transactions
   */
  static async getFilteredTransactions(
    filters: InventoryTransactionFilters,
    limit: number = 50,
    offset: number = 0
  ): Promise<{ transactions: InventoryTransaction[]; total: number }> {
    const whereClause: any = {
      organization_id: filters.organization_id
    };

    // Add optional filters
    if (filters.product_id) {
      whereClause.product_id = filters.product_id;
    }

    if (filters.sku_id) {
      whereClause.sku_id = filters.sku_id;
    }

    if (filters.transaction_type) {
      whereClause.transaction_type = filters.transaction_type;
    }

    if (filters.reference_type) {
      whereClause.reference_type = filters.reference_type;
    }

    if (filters.reference_id) {
      whereClause.reference_id = filters.reference_id;
    }

    if (filters.performed_by) {
      whereClause.performed_by = filters.performed_by;
    }

    if (filters.date_from || filters.date_to) {
      whereClause.created_at = {};
      if (filters.date_from) {
        whereClause.created_at[Op.gte] = filters.date_from;
      }
      if (filters.date_to) {
        whereClause.created_at[Op.lte] = filters.date_to;
      }
    }

    const { rows: transactions, count: total } = await InventoryTransaction.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Product,
          attributes: ['name', 'category']
        },
        {
          model: SKU,
          attributes: ['sku_code', 'unit_type']
        },
        {
          model: User,
          as: 'performer',
          attributes: ['name', 'email']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    return { transactions, total };
  }

  /**
   * Create a return to inventory transaction for GRN
   */
  static async createGRNReturnTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    returnQuantity: number,
    grnRequestId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'return_to_inventory',
      quantity_change: returnQuantity, // Positive value for returns
      reference_type: 'grn_return',
      reference_id: grnRequestId,
      performed_by: performedBy,
      reason: 'GRN Return - Items returned to inventory',
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a sale transaction (negative quantity change)
   */
  static async createSaleTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    saleQuantity: number,
    salesOrderId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'sale',
      quantity_change: -Math.abs(saleQuantity), // Negative value for sales
      reference_type: 'sales_order',
      reference_id: salesOrderId,
      performed_by: performedBy,
      reason: 'Sale - Items sold to customer',
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a purchase transaction (positive quantity change)
   */
  static async createPurchaseTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    purchaseQuantity: number,
    purchaseRecordId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'purchase',
      quantity_change: Math.abs(purchaseQuantity), // Positive value for purchases
      reference_type: 'purchase_record',
      reference_id: purchaseRecordId,
      performed_by: performedBy,
      reason: 'Purchase - Items added from supplier',
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a vehicle arrival transaction (positive quantity change)
   */
  static async createVehicleArrivalTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    arrivalQuantity: number,
    vehicleArrivalId: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'vehicle_arrival',
      quantity_change: Math.abs(arrivalQuantity), // Positive value for arrivals
      reference_type: 'vehicle_arrival',
      reference_id: vehicleArrivalId,
      performed_by: performedBy,
      reason: 'Vehicle Arrival - Items received from vehicle',
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }

  /**
   * Create a manual adjustment transaction
   */
  static async createAdjustmentTransaction(
    organizationId: string,
    productId: string,
    skuId: string,
    adjustmentQuantity: number,
    reason: string,
    performedBy?: string,
    notes?: string,
    transaction?: Transaction
  ): Promise<InventoryTransaction> {
    const transactionData: CreateInventoryTransactionData = {
      organization_id: organizationId,
      product_id: productId,
      sku_id: skuId,
      transaction_type: 'adjustment',
      quantity_change: adjustmentQuantity, // Can be positive or negative
      reference_type: 'manual_adjustment',
      reference_id: `adj_${Date.now()}`, // Generate a unique reference ID
      performed_by: performedBy,
      reason: reason,
      notes: notes
    };

    return await this.createInventoryTransaction(transactionData, transaction);
  }
}
