import {
  Table,
  Column,
  Model,
  <PERSON>Type,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Customer } from './Customer';
import { Organization } from './Organization';

@Table({
  tableName: 'sales_orders',
  timestamps: true,
  underscored: true,
})
export class SalesOrder extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.STRING)
  order_number!: string;

  @ForeignKey(() => Customer)
  @Column(DataType.UUID)
  customer_id!: string;

  @Column(DataType.DATE)
  order_date!: Date;

  @Column(DataType.DATE)
  delivery_date?: Date;

  @Column(DataType.TEXT)
  delivery_address?: string;

  @Column(DataType.INTEGER)
  payment_terms?: number;

  @Column(DataType.ENUM('cash', 'credit', 'online', 'cheque', 'multiple'))
  payment_mode!: string;

  @Column(DataType.ENUM('pending', 'partial', 'paid', 'overdue'))
  payment_status!: string;

  @Column(DataType.DECIMAL(10, 2))
  subtotal?: number;

  @Column(DataType.DECIMAL(10, 2))
  tax_amount?: number;

  @Column(DataType.DECIMAL(10, 2))
  discount_amount?: number;

  @Column(DataType.DECIMAL(10, 2))
  total_amount?: number;

  @Column(DataType.ENUM('pending', 'pending_approval', 'confirmed', 'processing', 'dispatch_pending', 'dispatched', 'delivered', 'completed', 'cancelled'))
  status!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @Column(DataType.STRING)
  vehicle_number?: string;

  @Column(DataType.STRING)
  driver_name?: string;

  @Column(DataType.STRING)
  driver_contact?: string;

  @Default(false)
  @Column(DataType.BOOLEAN)
  delivery_location_confirmed!: boolean;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Customer)
  customer!: Customer;

  @BelongsTo(() => Organization)
  organization!: Organization;

  @HasMany(() => SalesOrderItem)
  items!: SalesOrderItem[];

  @HasMany(() => SalesOrderPayment)
  payments!: SalesOrderPayment[];
}

@Table({
  tableName: 'sales_order_items',
  timestamps: true,
  underscored: true,
})
export class SalesOrderItem extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => SalesOrder)
  @Column(DataType.UUID)
  sales_order_id!: string;

  @Column(DataType.UUID)
  product_id!: string;

  @Column(DataType.UUID)
  sku_id!: string;

  @Column(DataType.STRING)
  product_name!: string;

  @Column(DataType.STRING)
  sku_code!: string;

  @Column(DataType.INTEGER)
  quantity!: number;

  @Column(DataType.ENUM('box', 'loose'))
  unit_type!: string;

  @Column(DataType.DECIMAL(10, 2))
  unit_price!: number;

  @Column(DataType.DECIMAL(10, 2))
  total_price!: number;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => SalesOrder)
  sales_order!: SalesOrder;

  @BelongsTo(() => Organization)
  organization!: Organization;
}

@Table({
  tableName: 'sales_order_payments',
  timestamps: true,
  underscored: true,
})
export class SalesOrderPayment extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => SalesOrder)
  @Column(DataType.UUID)
  sales_order_id!: string;

  @Column(DataType.ENUM('cash', 'credit', 'online', 'cheque', 'upi'))
  payment_type!: string;

  @Column(DataType.DECIMAL(10, 2))
  amount!: number;

  @Column(DataType.STRING)
  reference_number?: string;

  @Column(DataType.STRING)
  proof_url?: string;

  @Column(DataType.TEXT)
  remarks?: string;

  @Column(DataType.ENUM('pending', 'confirmed', 'failed'))
  status!: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => SalesOrder)
  sales_order!: SalesOrder;

  @BelongsTo(() => Organization)
  organization!: Organization;
}
