import {
  Table,
  Column,
  Model,
  DataType,
  Primary<PERSON>ey,
  <PERSON>fault,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { Product, SKU } from './Product';

@Table({
  tableName: 'current_inventory',
  timestamps: true,
  underscored: true,
})
export class CurrentInventory extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => Product)
  @Column(DataType.UUID)
  product_id!: string;

  @ForeignKey(() => SKU)
  @Column(DataType.UUID)
  sku_id!: string;

  @Column(DataType.STRING)
  product_name!: string;

  @Column(DataType.STRING)
  sku_code!: string;

  @Column(DataType.STRING)
  category!: string;

  @Column(DataType.ENUM('box', 'loose'))
  unit_type!: string;

  @Default(0)
  @Column(DataType.INTEGER)
  available_quantity!: number;

  @Default(0)
  @Column(DataType.DECIMAL(10, 2))
  total_weight!: number;

  @Column(DataType.DATE)
  last_updated_at?: Date;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => Product)
  product!: Product;

  @BelongsTo(() => SKU)
  sku!: SKU;
}

// Export interfaces for backward compatibility
export interface CreateInventoryData {
  organization_id: string;
  product_id: string;
  sku_id: string;
  product_name: string;
  sku_code: string;
  category: string;
  unit_type: 'box' | 'loose';
  available_quantity: number;
  total_weight: number;
}

export interface UpdateInventoryData {
  available_quantity?: number;
  total_weight?: number;
  last_updated_at?: Date;
}

export interface InventoryAdjustmentData {
  product_id: string;
  sku_id: string;
  adjustment_type: 'add' | 'subtract' | 'set';
  quantity_change: number;
  weight_change?: number;
  reason: string;
  notes?: string;
}
