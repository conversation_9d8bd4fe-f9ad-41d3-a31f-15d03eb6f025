import { DataTypes, Model, Optional } from 'sequelize';
import sequelize from '../config/sequelize';
import { Organization } from './Organization';
import { Product } from './Product';
import { SKU } from './Product';
import { User } from './User';

export interface InventoryDumpAttributes {
  id: string;
  organization_id: string;
  product_id: string;
  sku_id: string;
  product_name: string;
  sku_code: string;
  dumped_quantity: number;
  dumped_weight: number;
  reason: 'damaged' | 'expired' | 'quality_issue' | 'contaminated' | 'other';
  reason_notes?: string;
  dumped_by: string;
  dumped_at: Date;
  created_at: Date;
  updated_at: Date;
}

export interface InventoryDumpCreationAttributes extends Optional<InventoryDumpAttributes, 'id' | 'dumped_weight' | 'reason_notes' | 'dumped_at' | 'created_at' | 'updated_at'> {}

export class InventoryDump extends Model<InventoryDumpAttributes, InventoryDumpCreationAttributes> implements InventoryDumpAttributes {
  public id!: string;
  public organization_id!: string;
  public product_id!: string;
  public sku_id!: string;
  public product_name!: string;
  public sku_code!: string;
  public dumped_quantity!: number;
  public dumped_weight!: number;
  public reason!: 'damaged' | 'expired' | 'quality_issue' | 'contaminated' | 'other';
  public reason_notes?: string;
  public dumped_by!: string;
  public dumped_at!: Date;
  public created_at!: Date;
  public updated_at!: Date;

  // Associations
  public organization?: Organization;
  public product?: Product;
  public sku?: SKU;
  public dumper?: User;
}

InventoryDump.init(
  {
    id: {
      type: DataTypes.STRING(36),
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4,
    },
    organization_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: Organization,
        key: 'id',
      },
    },
    product_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: Product,
        key: 'id',
      },
    },
    sku_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: SKU,
        key: 'id',
      },
    },
    product_name: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    sku_code: {
      type: DataTypes.STRING(100),
      allowNull: false,
    },
    dumped_quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
      },
    },
    dumped_weight: {
      type: DataTypes.DECIMAL(10, 3),
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0,
      },
    },
    reason: {
      type: DataTypes.ENUM('damaged', 'expired', 'quality_issue', 'contaminated', 'other'),
      allowNull: false,
    },
    reason_notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    dumped_by: {
      type: DataTypes.STRING(36),
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    dumped_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'InventoryDump',
    tableName: 'inventory_dumps',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      {
        fields: ['organization_id'],
      },
      {
        fields: ['product_id', 'sku_id'],
      },
      {
        fields: ['dumped_at'],
      },
      {
        fields: ['reason'],
      },
      {
        fields: ['dumped_by'],
      },
    ],
  }
);

// Define associations
InventoryDump.belongsTo(Organization, {
  foreignKey: 'organization_id',
  as: 'organization',
});

InventoryDump.belongsTo(Product, {
  foreignKey: 'product_id',
  as: 'product',
});

InventoryDump.belongsTo(SKU, {
  foreignKey: 'sku_id',
  as: 'sku',
});

InventoryDump.belongsTo(User, {
  foreignKey: 'dumped_by',
  as: 'dumper',
});

export default InventoryDump;
