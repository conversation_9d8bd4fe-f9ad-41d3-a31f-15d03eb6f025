import {
  Table,
  Column,
  Model,
  DataType,
  <PERSON><PERSON>ey,
  <PERSON><PERSON>ult,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Organization } from './Organization';

@Table({
  tableName: 'customers',
  timestamps: true,
  underscored: true,
})
export class Customer extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.STRING)
  name!: string;

  @Column(DataType.ENUM('individual', 'business'))
  customer_type!: string;

  @Column(DataType.STRING)
  contact!: string;

  @Column(DataType.STRING)
  email!: string;

  @Column(DataType.TEXT)
  address!: string;

  @Column(DataType.JSON)
  delivery_addresses?: any[];

  @Column(DataType.STRING)
  gst_number?: string;

  @Column(DataType.STRING)
  pan_number?: string;

  @<PERSON><PERSON>ult(0)
  @Column(DataType.DECIMAL(10, 2))
  credit_limit!: number;

  @Default(0)
  @Column(DataType.DECIMAL(10, 2))
  current_balance!: number;

  @Default(30)
  @Column(DataType.INTEGER)
  payment_terms!: number;

  @Default('active')
  @Column(DataType.ENUM('active', 'inactive'))
  status!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;
}

// Export interfaces for backward compatibility
export interface CreateCustomerData {
  organization_id: string;
  name: string;
  customer_type?: 'individual' | 'business';
  contact: string;
  email: string;
  address: string;
  delivery_addresses?: any[];
  gst_number?: string;
  pan_number?: string;
  credit_limit?: number;
  payment_terms?: number;
  notes?: string;
}

export interface UpdateCustomerData {
  name?: string;
  customer_type?: 'individual' | 'business';
  contact?: string;
  email?: string;
  address?: string;
  delivery_addresses?: any[];
  gst_number?: string;
  pan_number?: string;
  credit_limit?: number;
  current_balance?: number;
  payment_terms?: number;
  status?: 'active' | 'inactive';
  notes?: string;
}
