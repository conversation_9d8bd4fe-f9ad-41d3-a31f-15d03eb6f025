import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  Default,
  CreatedAt,
  UpdatedAt,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Organization } from './Organization';
import { Product, SKU } from './Product';
import { PurchaseRecord } from './PurchaseRecord';

@Table({
  tableName: 'vehicle_arrivals',
  timestamps: true,
  underscored: true,
})
export class VehicleArrival extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @Column(DataType.STRING)
  vehicle_number?: string;

  @Column(DataType.STRING)
  supplier!: string;

  @Column(DataType.STRING)
  driver_name?: string;

  @Column(DataType.STRING)
  driver_contact?: string;

  @Column(DataType.DATE)
  arrival_time!: Date;

  @Column(DataType.STRING)
  source_location?: string;

  @Default('pending')
  @Column(DataType.ENUM('pending', 'in_progress', 'completed', 'cancelled', 'po-created'))
  status!: string;

  @Column(DataType.TEXT)
  notes?: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @HasMany(() => VehicleArrivalItem)
  items!: VehicleArrivalItem[];

  @HasMany(() => VehicleArrivalAttachment)
  attachments!: VehicleArrivalAttachment[];

  @HasMany(() => PurchaseRecord)
  purchase_records!: PurchaseRecord[];
}

@Table({
  tableName: 'vehicle_arrival_items',
  timestamps: true,
  underscored: true,
})
export class VehicleArrivalItem extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => VehicleArrival)
  @Column(DataType.UUID)
  vehicle_arrival_id!: string;

  @ForeignKey(() => Product)
  @Column(DataType.UUID)
  product_id!: string;

  @ForeignKey(() => SKU)
  @Column(DataType.UUID)
  sku_id!: string;

  @Column(DataType.ENUM('box', 'loose'))
  unit_type!: string;

  @Column(DataType.DECIMAL(8, 2))
  unit_weight?: number;

  @Column(DataType.INTEGER)
  quantity!: number;

  @Column(DataType.DECIMAL(10, 2))
  total_weight!: number;

  @Column(DataType.INTEGER)
  final_quantity?: number;

  @Column(DataType.DECIMAL(10, 2))
  final_total_weight?: number;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => VehicleArrival)
  vehicle_arrival!: VehicleArrival;

  @BelongsTo(() => Product)
  product!: Product;

  @BelongsTo(() => SKU)
  sku!: SKU;
}

@Table({
  tableName: 'vehicle_arrival_attachments',
  timestamps: true,
  underscored: true,
})
export class VehicleArrivalAttachment extends Model {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column(DataType.UUID)
  id!: string;

  @ForeignKey(() => Organization)
  @Column(DataType.UUID)
  organization_id!: string;

  @ForeignKey(() => VehicleArrival)
  @Column(DataType.UUID)
  vehicle_arrival_id!: string;

  @Column(DataType.STRING)
  file_name!: string;

  @Column(DataType.STRING)
  file_type!: string;

  @Column(DataType.INTEGER)
  file_size!: number;

  @Column(DataType.STRING)
  file_url!: string;

  @CreatedAt
  created_at!: Date;

  @UpdatedAt
  updated_at!: Date;

  // Associations
  @BelongsTo(() => Organization)
  organization!: Organization;

  @BelongsTo(() => VehicleArrival)
  vehicle_arrival!: VehicleArrival;
}
