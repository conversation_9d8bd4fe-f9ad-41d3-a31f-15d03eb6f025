{"name": "fxd_partner_erp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "migrate": "ts-node src/scripts/migrate.ts", "seed-users": "ts-node src/scripts/seed-users.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.13", "@types/uuid": "^10.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.14.1", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.7", "sequelize-typescript": "^2.1.6"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/morgan": "^1.9.10", "@types/node": "^24.0.4", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}